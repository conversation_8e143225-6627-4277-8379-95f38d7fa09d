# 可配置质量评估系统配置文件
# 支持不同任务类型的自定义评估标准和权重配置

tasks:
  # 数学推理任务 (GSM8K, MATH)
  gsm8k:
    dimensions:
      accuracy:
        weight: 0.35
        description: "Mathematical correctness and accuracy of calculations"
        prompt_template: "Evaluate the mathematical accuracy of this solution. Check all calculations, formulas, and final answer."
        scoring_rubric:
          "0.9-1.0": "All calculations correct, final answer accurate"
          "0.7-0.8": "Minor calculation errors, mostly correct approach"
          "0.5-0.6": "Some calculation errors, partially correct"
          "0.3-0.4": "Significant calculation errors"
          "0.0-0.2": "Mostly incorrect calculations"
        enabled: true
      
      completeness:
        weight: 0.25
        description: "Whether all parts of the problem are addressed with proper steps"
        prompt_template: "Evaluate if the solution addresses all aspects of the math problem with clear steps."
        enabled: true
      
      clarity:
        weight: 0.20
        description: "Clarity of mathematical reasoning and step-by-step explanation"
        prompt_template: "Evaluate how clearly the mathematical reasoning is explained step by step."
        enabled: true
      
      coherence:
        weight: 0.15
        description: "Logical flow and consistency of mathematical reasoning"
        enabled: true
      
      efficiency:
        weight: 0.05
        description: "Efficiency of the mathematical approach used"
        enabled: true
    
    thresholds:
      approval: 0.90
      minor_revision: 0.75
      major_revision: 0.50
      rejection: 0.30
    
    evaluation_focus: ["accuracy", "completeness", "clarity"]
    
    special_requirements:
      requires_step_by_step: true
      check_calculations: true
      verify_units: true
    
    custom_prompts:
      final_check: "Verify that the mathematical solution is complete and accurate."

  # 数学竞赛题 (MATH)
  math:
    dimensions:
      accuracy:
        weight: 0.40
        description: "Mathematical rigor and correctness for competition-level problems"
        enabled: true
      
      completeness:
        weight: 0.25
        description: "Complete solution with all necessary steps and justifications"
        enabled: true
      
      clarity:
        weight: 0.20
        description: "Clear mathematical exposition suitable for competition standards"
        enabled: true
      
      elegance:
        weight: 0.15
        description: "Elegance and sophistication of the mathematical approach"
        prompt_template: "Evaluate the elegance and mathematical sophistication of this solution."
        enabled: true
    
    thresholds:
      approval: 0.92
      minor_revision: 0.78
      major_revision: 0.55
      rejection: 0.35
    
    evaluation_focus: ["accuracy", "completeness", "elegance"]
    
    special_requirements:
      requires_step_by_step: true
      check_calculations: true
      verify_proofs: true
      competition_level: true

  # 代码生成任务 (MBPP, HumanEval)
  mbpp:
    dimensions:
      accuracy:
        weight: 0.30
        description: "Functional correctness and bug-free implementation"
        prompt_template: "Evaluate if the code correctly implements the required functionality without bugs."
        enabled: true
      
      efficiency:
        weight: 0.25
        description: "Time and space complexity, algorithmic efficiency"
        prompt_template: "Evaluate the efficiency of the algorithm and implementation."
        enabled: true
      
      clarity:
        weight: 0.20
        description: "Code readability, structure, and documentation"
        prompt_template: "Evaluate code readability, structure, comments, and overall clarity."
        enabled: true
      
      completeness:
        weight: 0.15
        description: "Handles all specified requirements and edge cases"
        enabled: true
      
      style:
        weight: 0.10
        description: "Adherence to coding conventions and best practices"
        prompt_template: "Evaluate adherence to Python coding conventions and best practices."
        enabled: true
    
    thresholds:
      approval: 0.85
      minor_revision: 0.70
      major_revision: 0.50
      rejection: 0.30
    
    evaluation_focus: ["accuracy", "efficiency", "clarity"]
    
    special_requirements:
      check_syntax: true
      test_functionality: true
      verify_edge_cases: true
      python_conventions: true

  # 阅读理解任务 (DROP, HotpotQA)
  drop:
    dimensions:
      accuracy:
        weight: 0.30
        description: "Correctness of extracted information and reasoning"
        enabled: true
      
      relevance:
        weight: 0.25
        description: "How well the answer addresses the specific question"
        prompt_template: "Evaluate how directly and relevantly the answer addresses the question."
        enabled: true
      
      completeness:
        weight: 0.20
        description: "Comprehensive coverage of all required information"
        enabled: true
      
      clarity:
        weight: 0.15
        description: "Clear and understandable presentation of the answer"
        enabled: true
      
      evidence_support:
        weight: 0.10
        description: "Proper use of evidence from the source text"
        prompt_template: "Evaluate how well the answer is supported by evidence from the text."
        enabled: true
    
    thresholds:
      approval: 0.80
      minor_revision: 0.65
      major_revision: 0.45
      rejection: 0.25
    
    evaluation_focus: ["relevance", "accuracy", "evidence_support"]
    
    special_requirements:
      check_text_comprehension: true
      verify_evidence_usage: true
      multi_hop_reasoning: true

  # 策略问答 (StrategyQA)
  strategyqa:
    dimensions:
      accuracy:
        weight: 0.25
        description: "Correctness of the yes/no answer and reasoning"
        enabled: true
      
      reasoning_quality:
        weight: 0.30
        description: "Quality and depth of strategic reasoning"
        prompt_template: "Evaluate the quality and depth of strategic reasoning provided."
        enabled: true
      
      relevance:
        weight: 0.20
        description: "Relevance of reasoning to the strategic question"
        enabled: true
      
      clarity:
        weight: 0.15
        description: "Clear explanation of the reasoning process"
        enabled: true
      
      logical_consistency:
        weight: 0.10
        description: "Internal consistency of the reasoning chain"
        enabled: true
    
    thresholds:
      approval: 0.78
      minor_revision: 0.62
      major_revision: 0.42
      rejection: 0.22
    
    evaluation_focus: ["reasoning_quality", "accuracy", "logical_consistency"]
    
    special_requirements:
      strategic_thinking: true
      binary_answer_required: true

  # 科学问答 (GPQA)
  gpqa:
    dimensions:
      accuracy:
        weight: 0.35
        description: "Scientific accuracy and factual correctness"
        enabled: true
      
      scientific_rigor:
        weight: 0.25
        description: "Adherence to scientific principles and methodology"
        prompt_template: "Evaluate the scientific rigor and adherence to scientific principles."
        enabled: true
      
      completeness:
        weight: 0.20
        description: "Comprehensive coverage of scientific concepts"
        enabled: true
      
      clarity:
        weight: 0.15
        description: "Clear scientific explanation"
        enabled: true
      
      evidence_based:
        weight: 0.05
        description: "Use of scientific evidence and reasoning"
        enabled: true
    
    thresholds:
      approval: 0.88
      minor_revision: 0.72
      major_revision: 0.52
      rejection: 0.32
    
    evaluation_focus: ["accuracy", "scientific_rigor", "completeness"]
    
    special_requirements:
      scientific_accuracy: true
      graduate_level: true

  # 多选题 (MMLU)
  mmlu:
    dimensions:
      accuracy:
        weight: 0.40
        description: "Correctness of the selected answer"
        enabled: true
      
      reasoning:
        weight: 0.30
        description: "Quality of reasoning leading to the answer"
        enabled: true
      
      relevance:
        weight: 0.20
        description: "Relevance of explanation to the question domain"
        enabled: true
      
      confidence:
        weight: 0.10
        description: "Appropriate confidence level in the answer"
        enabled: true
    
    thresholds:
      approval: 0.82
      minor_revision: 0.68
      major_revision: 0.48
      rejection: 0.28
    
    evaluation_focus: ["accuracy", "reasoning"]
    
    special_requirements:
      multiple_choice: true
      domain_expertise: true

# 全局设置
global_settings:
  default_temperature: 0.2
  max_evaluation_tokens: 2048
  enable_dimension_optimization: true
  save_evaluation_history: true
  
# 权重优化设置
optimization:
  enable_auto_optimization: false
  optimization_threshold: 10  # 最少评估次数后才开始优化
  target_performance: 0.80
  weight_adjustment_step: 0.05
