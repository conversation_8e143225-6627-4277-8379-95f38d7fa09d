#!/bin/bash

trap 'echo -e "\n[INFO] Script interrupted by user. Exiting..."; exit 130' INT TERM

mkdir -p "../results/benchmark_outputs"

/opt/anaconda3/envs/MAS/bin/python ../main.py \
  --jsonl "../benchmark/gsm8k.jsonl" \
  --models openai deepseek \
  --max_problems 2 \
  --output "../results/benchmark_outputs/gsm8k_sample_result.json" \
  --context-optimization \
  --leader "${LEADER_AGENT:-openai}" \
  --debug