#!/bin/bash

# Ablation Study 自动化脚本
# 对比不同系统变体的性能和token消耗

trap 'kill $(jobs -p) 2>/dev/null; exit 130' INT TERM

export MODELS="${MODELS:-openai anthropic llama}"
export LEADER_AGENT="${LEADER_AGENT:-gemini}"
export MAX_PROBLEMS="${MAX_PROBLEMS:-10}"
export OUTPUT_DIR="/Users/<USER>/Desktop/LLM_MAS/results/ablation"
mkdir -p "${OUTPUT_DIR}"

# 解析参数
DATASETS="gsm8k math mbpp"  # 默认测试数据集
QUICK_MODE=false

case "${1:-all}" in
    --quick)
        export MAX_PROBLEMS=3
        QUICK_MODE=true
        shift
        ;;
    --datasets)
        DATASETS="$2"
        shift 2
        ;;
esac

if [ "$#" -gt 0 ]; then
    DATASETS="$*"
fi

echo "🧪 Starting Ablation Study"
echo "Datasets: ${DATASETS}"
echo "Problems per dataset: ${MAX_PROBLEMS}"
echo "Models: ${MODELS}"
echo ""

# 运行不同变体
run_variant() {
    local variant="$1"
    local dataset="$2"
    local extra_args="$3"
    
    echo "🔬 Running ${variant} on ${dataset}..."
    
    source ~/.zshrc && /opt/anaconda3/envs/MAS/bin/python /Users/<USER>/Desktop/LLM_MAS/main.py \
        --jsonl "/Users/<USER>/Desktop/LLM_MAS/benchmark/${dataset}.jsonl" \
        --max_problems "${MAX_PROBLEMS}" \
        --models ${MODELS} \
        --leader "${LEADER_AGENT}" \
        --output "${OUTPUT_DIR}/${variant}_${dataset}_result.json" \
        ${extra_args} \
        > "${OUTPUT_DIR}/${variant}_${dataset}_log.txt" 2>&1
}

# 变体定义 - 使用函数替代关联数组
get_variant_args() {
    case "$1" in
        "baseline") echo "--baseline-mode" ;;
        "no_merge") echo "--context-optimization --disable-merge" ;;
        "no_annotation") echo "--context-optimization --disable-annotation" ;;
        "no_compressor") echo "--context-optimization --disable-compressor" ;;
        "full_system") echo "--context-optimization" ;;
        *) echo "--context-optimization" ;;
    esac
}

# 运行所有变体
for variant in baseline no_merge no_annotation no_compressor full_system; do
    echo "📊 Testing variant: ${variant}"
    
    for dataset in ${DATASETS}; do
        run_variant "${variant}" "${dataset}" "$(get_variant_args ${variant})"
    done
    
    echo "✅ ${variant} completed"
    echo ""
done

echo "📈 Generating ablation results..."

# 生成结果分析
python3 << 'EOF'
import json
import csv
import os
import glob
from pathlib import Path

def extract_metrics(result_file):
    """从结果文件提取指标"""
    try:
        with open(result_file, 'r') as f:
            data = json.load(f)
        
        results = data.get('results', [])
        if not results:
            return None
        
        # 计算准确率
        total = len(results)
        correct = sum(1 for r in results if r.get('success', False))
        accuracy = correct / total if total > 0 else 0
        
        # 计算平均token使用
        avg_tokens = 0
        token_counts = []
        for r in results:
            if 'efficiency_metrics' in r:
                tokens = r['efficiency_metrics'].get('total_tokens', 0)
                if tokens > 0:
                    token_counts.append(tokens)
        
        if token_counts:
            avg_tokens = sum(token_counts) / len(token_counts)
        
        # 计算平均时间
        avg_time = 0
        time_counts = []
        for r in results:
            time_val = r.get('processing_time', 0)
            if time_val > 0:
                time_counts.append(time_val)
        
        if time_counts:
            avg_time = sum(time_counts) / len(time_counts)
        
        return {
            'accuracy': accuracy,
            'avg_tokens': avg_tokens,
            'avg_time': avg_time,
            'total_problems': total,
            'correct_problems': correct
        }
    
    except Exception as e:
        print(f"Error processing {result_file}: {e}")
        return None

# 收集所有结果
output_dir = "/Users/<USER>/Desktop/LLM_MAS/results/ablation"
results = []

variants = ['baseline', 'no_merge', 'no_annotation', 'no_compressor', 'full_system']
datasets = ['gsm8k', 'math', 'mbpp']

for variant in variants:
    for dataset in datasets:
        result_file = f"{output_dir}/{variant}_{dataset}_result.json"
        
        if os.path.exists(result_file):
            metrics = extract_metrics(result_file)
            if metrics:
                results.append({
                    'variant': variant,
                    'dataset': dataset,
                    **metrics
                })

# 保存到CSV
csv_file = f"{output_dir}/ablation_results.csv"
if results:
    with open(csv_file, 'w', newline='') as f:
        writer = csv.DictWriter(f, fieldnames=[
            'variant', 'dataset', 'accuracy', 'avg_tokens', 'avg_time', 
            'total_problems', 'correct_problems'
        ])
        writer.writeheader()
        writer.writerows(results)
    
    print(f"✅ Results saved to {csv_file}")
    
    # 打印摘要
    print("\n📊 Ablation Study Summary:")
    print("=" * 60)
    
    # 按变体汇总
    variant_summary = {}
    for result in results:
        variant = result['variant']
        if variant not in variant_summary:
            variant_summary[variant] = {
                'accuracy': [],
                'tokens': [],
                'time': []
            }
        
        variant_summary[variant]['accuracy'].append(result['accuracy'])
        variant_summary[variant]['tokens'].append(result['avg_tokens'])
        variant_summary[variant]['time'].append(result['avg_time'])
    
    for variant, data in variant_summary.items():
        avg_acc = sum(data['accuracy']) / len(data['accuracy']) if data['accuracy'] else 0
        avg_tokens = sum(data['tokens']) / len(data['tokens']) if data['tokens'] else 0
        avg_time = sum(data['time']) / len(data['time']) if data['time'] else 0
        
        print(f"{variant:15} | Acc: {avg_acc:.3f} | Tokens: {avg_tokens:6.0f} | Time: {avg_time:5.1f}s")
    
    print("=" * 60)
else:
    print("⚠️ No results found")

EOF

echo ""
echo "🎉 Ablation study completed!"
echo "📁 Results saved to: ${OUTPUT_DIR}/"
echo "📊 CSV summary: ${OUTPUT_DIR}/ablation_results.csv"
echo ""
echo "💡 Next steps:"
echo "  1. Review ablation_results.csv"
echo "  2. Generate visualization charts"
echo "  3. Include in paper Results section"
