#!/bin/bash
# Convenience script to run all baseline experiments

# Handle Ctrl+C gracefully
trap 'kill $(jobs -p) 2>/dev/null; exit 130' INT TERM

export BASELINE_DIR="/Users/<USER>/Desktop/LLM_MAS/baseline"
export OUTPUT_DIR="/Users/<USER>/Desktop/LLM_MAS/results_baseline"

mkdir -p "${OUTPUT_DIR}"

# Default model (can be overridden by environment variable)
MODEL="${BASELINE_MODEL:-openai}"


python "${BASELINE_DIR}/run_baselines.py" \
    --method all \
    --dataset all \
    --model "${MODEL}" \
    --max-problems 100 \
    --parallel