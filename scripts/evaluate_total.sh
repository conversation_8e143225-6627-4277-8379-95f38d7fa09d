#!/bin/bash
# Comprehensive evaluation script for all methods (baselines + our methods)
# Usage: ./scripts/evaluate_total.sh [dataset] [method]

# Handle Ctrl+C gracefully
trap 'kill $(jobs -p) 2>/dev/null; exit 130' INT TERM

export BASELINE_DIR="/Users/<USER>/Desktop/LLM_MAS/baseline"
export RESULTS_DIR="/Users/<USER>/Desktop/LLM_MAS/results_baseline"
export EVAL_OUTPUT_DIR="/Users/<USER>/Desktop/LLM_MAS/results_baseline/evaluation"
export OUR_RESULTS_DIR="/Users/<USER>/Desktop/LLM_MAS/results"

# Parse arguments
DATASET=${1:-"all"}
METHOD=${2:-"all"}

echo "🚀 Starting Comprehensive Evaluation (Baselines + Our Methods)"
echo "Dataset: $DATASET | Method: $METHOD"
echo ""

# First convert baseline files to evaluation format
echo "📋 Converting baseline results to evaluation format..."
python "${BASELINE_DIR}/convert_baseline_format.py" > /dev/null

# Run comprehensive evaluation script
echo "🔍 Running comprehensive evaluation..."
python "${BASELINE_DIR}/evaluate_total.py" \
    --baseline-dir "${RESULTS_DIR}" \
    --eval-output-dir "${EVAL_OUTPUT_DIR}" \
    --our-results-dir "${OUR_RESULTS_DIR}" \
    --method "${METHOD}" \
    --dataset "${DATASET}" \
    --summary


