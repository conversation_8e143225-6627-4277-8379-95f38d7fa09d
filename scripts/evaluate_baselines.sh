#!/bin/bash
# Convenience script to evaluate all baseline results + our methods

# Handle Ctrl+C gracefully
trap 'kill $(jobs -p) 2>/dev/null; exit 130' INT TERM

export BASELINE_DIR="/Users/<USER>/Desktop/LLM_MAS_with baseline/baseline"
export RESULTS_DIR="/Users/<USER>/Desktop/LLM_MAS_with baseline/results_baseline"
export EVAL_OUTPUT_DIR="/Users/<USER>/Desktop/LLM_MAS_with baseline/results_baseline/evaluation"

echo "Step 0: Cleaning evaluation cache..."
rm -rf "${EVAL_OUTPUT_DIR}"/*

echo "Step 1: Converting baseline results to evaluator format..."
python "${BASELINE_DIR}/convert_baseline_format.py"

echo "Step 2: Fixing ID mismatches..."
python "${BASELINE_DIR}/fix_baseline_ids.py"

echo "Step 3: Running main evaluator on baseline results..."
python "${BASELINE_DIR}/evaluate_baselines.py" --method all --dataset all

echo "Step 4: Generating summary comparison..."

# Run comprehensive evaluation script (baselines + our methods)
python "${BASELINE_DIR}/evaluate_total.py" \
    --baseline-dir "${RESULTS_DIR}" \
    --eval-output-dir "${EVAL_OUTPUT_DIR}" \
    --our-results-dir "/Users/<USER>/Desktop/LLM_MAS_with baseline/results" \
    --method all \
    --dataset all \
    --summary