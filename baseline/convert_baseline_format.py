#!/usr/bin/env python3
"""
Convert baseline result files to the format expected by the evaluator
"""

import json
import os
import sys
from typing import List, Dict, Any

def convert_baseline_file(input_file: str, output_file: str) -> None:
    """Convert a baseline result file to evaluator format"""

    with open(input_file, 'r') as f:
        baseline_results = json.load(f)

    # Convert to evaluator format
    converted_results = {
        "results": []
    }

    for item in baseline_results:
        converted_item = {
            "id": item["id"],
            "prompt": item["question"],
            "prediction": item["answer"],  # 模型的回答
            "solution": item["answer"],    # 兼容性字段
            "consensus": item["answer"],   # 兼容性字段
            "response": item["answer"],    # 兼容性字段
            "execution_time": item.get("execution_time", 0.0)
        }
        converted_results["results"].append(converted_item)

    # Write converted file
    with open(output_file, 'w') as f:
        json.dump(converted_results, f, indent=2)

    print(f"✅ Converted {input_file} -> {output_file}")
    print(f"   Converted {len(baseline_results)} results")

def main():
    baseline_dir = "results_baseline"
    methods = ['direct_io', 'cot', 'cot_sc', 'cod', 'llm_debate']
    datasets = ['gsm8k', 'math', 'mbpp', 'humaneval', 'drop', 'hotpotqa', 'strategyqa', 'gpqa', 'mmlu']

    converted_count = 0

    for method in methods:
        method_dir = os.path.join(baseline_dir, method)
        if not os.path.exists(method_dir):
            continue

        print(f"\n🔄 Converting {method} results...")

        for dataset in datasets:
            input_file = os.path.join(method_dir, f"{dataset}_result.json")
            output_file = os.path.join(method_dir, f"{dataset}_result_eval_format.json")

            if os.path.exists(input_file):
                try:
                    convert_baseline_file(input_file, output_file)
                    converted_count += 1
                except Exception as e:
                    print(f"❌ Error converting {input_file}: {e}")

    print(f"\n🎉 Conversion completed!")
    print(f"Total files converted: {converted_count}")

if __name__ == "__main__":
    main()