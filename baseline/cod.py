#!/usr/bin/env python3
"""
Chain of Draft (CoD) Baseline
Prompts the model to think step by step with minimal drafts (5 words max per step)
"""

from typing import Dict, Any
from .base_baseline import BaseBaseline


class CoDBaseline(BaseBaseline):
    """Chain of Draft baseline - encourages step-by-step reasoning with minimal drafts"""

    def get_method_name(self) -> str:
        return "cod"
    
    async def generate_answer(self, problem: Dict[str, Any]) -> str:
        """Generate answer with step-by-step minimal draft reasoning"""

        # Extract question from different possible keys
        # Handle different dataset formats
        if 'context' in problem and isinstance(problem['context'], list):
            # HotpotQA format - build context + question
            context = problem['context']
            context_text = ""
            for ctx in context:
                if isinstance(ctx, list) and len(ctx) >= 2:
                    title = ctx[0]
                    paragraphs = ctx[1]
                    if isinstance(paragraphs, list):
                        context_text += f"\n{title}:\n" + " ".join(paragraphs) + "\n"
                    else:
                        context_text += f"\n{title}:\n{paragraphs}\n"
            question = f"Context: {context_text.strip()}\n\nQuestion: {problem.get('question', '')}"
        elif 'context' in problem:
            # DROP dataset format: context contains passage + question
            question = problem.get('context', '')
        else:
            # Standard format: prompt, question, or problem fields
            question = problem.get('prompt') or problem.get('question') or problem.get('problem') or ''

        # Handle case where question might be a list (e.g., hotpotqa)
        if isinstance(question, list):
            question = ' '.join(str(x) for x in question)
        elif not isinstance(question, str):
            question = str(question)

        # Detect dataset type for specialized prompts
        dataset_type = self._detect_dataset_type(problem)

        # Create specialized Chain of Draft prompt based on dataset
        prompt = self._create_specialized_prompt(dataset_type, question)

        # Get answer from API with higher temperature for more diverse reasoning
        answer = await self._api_call(prompt, temperature=0.7)
        return answer

    def _detect_dataset_type(self, problem: Dict[str, Any]) -> str:
        """Detect the dataset type based on problem structure"""

        # Check for specific dataset indicators
        if 'task_id' in problem and 'HumanEval' in str(problem.get('task_id', '')):
            return 'humaneval'
        elif 'context' in problem and isinstance(problem['context'], list):
            return 'hotpotqa'
        elif 'context' in problem:
            return 'drop'
        elif 'prompt' in problem and 'def ' in str(problem.get('prompt', '')):
            return 'humaneval'
        elif 'question' in problem and '?' in str(problem.get('question', '')):
            return 'strategyqa' if 'yes' in str(problem.get('question', '')).lower() or 'no' in str(problem.get('question', '')).lower() else 'gsm8k'

        return 'gsm8k'  # Default fallback

    def _create_specialized_prompt(self, dataset_type: str, question: str) -> str:
        """Create specialized Chain of Draft prompt based on dataset type"""

        if dataset_type == 'humaneval':
            return f"""Think step by step with minimal drafts (5 words max per step), then code.

{question}

Let's think step by step with minimal drafts:
1. Parse requirements: [5 words max]
2. Design algorithm: [5 words max]
3. Handle edge cases: [5 words max]
4. Code implementation: [5 words max]

Then provide ONLY your complete code solution without extra explanation."""

        elif dataset_type == 'hotpotqa':
            return f"""Answer using minimal drafts (5 words max per step).

{question}

Let's think step by step with minimal drafts:
1. Key info needed:
2. Find facts:
3. Connect logically:
4. Final answer:

Then provide your final answer."""

        elif dataset_type == 'drop':
            return f"""Answer with minimal drafts (5 words max per step).

{question}

Let's think step by step with minimal drafts:
1. Question asks:
2. Find info:
3. Calculate if needed:
4. Answer:

IMPORTANT: For numerical answers, end with:
#### [number]"""

        else:
            # Default prompt for math problems and others
            return f"""Think step by step, but only keep a minimum draft for each thinking step, with 5 words at most.

Question: {question}

Let's think step by step with minimal drafts:

IMPORTANT: For math problems, you MUST end your response with the final numerical answer in this exact format:
#### [number]

Example: If the answer is 42, end with:
#### 42"""