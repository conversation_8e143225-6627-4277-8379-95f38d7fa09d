#!/usr/bin/env python3
"""
Simple Baseline Evaluation Script
Direct evaluation for each baseline method in its own directory
"""

import asyncio
import argparse
import os
import sys
import subprocess
from typing import List, Dict, Any

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

BASELINE_METHODS = ['direct_io', 'cot', 'cot_sc', 'cod', 'llm_debate']
BENCHMARK_DATASETS = [
    'gsm8k', 'math', 'mbpp', 'humaneval',
    'drop', 'hotpotqa', 'strategyqa', 'gpqa', 'mmlu'
]


def run_evaluation_for_method(method: str, dataset: str, baseline_dir: str, eval_output_dir: str):
    """Run evaluation for a single method and dataset"""

    # Use the converted evaluator-format file
    result_file = os.path.join(baseline_dir, method, f"{dataset}_result_eval_format.json")
    if not os.path.exists(result_file):
        print(f"❌ Evaluator-format result file not found: {result_file}")
        return None

    # Create method-specific evaluation directory
    method_eval_dir = os.path.join(eval_output_dir, method)
    os.makedirs(method_eval_dir, exist_ok=True)
    
    # Run evaluation from the main directory but output to method-specific directory
    original_cwd = os.getcwd()
    
    try:
        # Create a symlink or copy approach, or use --output-dir if evaluate.py supports it
        # For now, let's run from main dir and then move the files
        cmd = [
            sys.executable,
            "evaluate.py",
            "--results-file", result_file,
            "--dataset", dataset,
            "--output-dir", method_eval_dir,  # Use method-specific directory
            "--no-use-answers"  # Use the baseline result file directly, not answers-only files
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=120, cwd=original_cwd)
        
        if result.returncode == 0:
            output = result.stdout
            
            # Move generated CSV files to method-specific directory
            import glob
            # More robust pattern matching for different datasets
            csv_patterns = [
                f"*{dataset}*.csv",  # General pattern
                f"*{dataset.upper()}*.csv",  # Uppercase pattern
                f"*{dataset}_*.csv",  # Underscore pattern
                f"{dataset}*.csv"   # Simple prefix pattern
            ]
            
            csv_files = []
            for pattern in csv_patterns:
                found_files = glob.glob(os.path.join(original_cwd, "results_baseline", pattern))
                csv_files.extend(found_files)
            
            # Remove duplicates
            csv_files = list(set(csv_files))
            
            for csv_file in csv_files:
                target_file = os.path.join(method_eval_dir, os.path.basename(csv_file))
                try:
                    import shutil
                    shutil.move(csv_file, target_file)
                except Exception as move_error:
                    pass
            
            # Extract accuracy and problem counts from output
            accuracy = None
            total_problems = None
            answered_problems = None
            correct_answers = None

            for line in output.split('\n'):
                if 'Accuracy:' in line:
                    # Extract accuracy percentage
                    accuracy_str = line.split('Accuracy:')[1].strip().split()[0]
                    try:
                        accuracy = float(accuracy_str.replace('%', ''))
                    except ValueError:
                        pass
                elif 'Total Problems:' in line:
                    try:
                        total_problems = int(line.split('Total Problems:')[1].strip())
                    except ValueError:
                        pass
                elif 'Answered Problems:' in line:
                    try:
                        answered_problems = int(line.split('Answered Problems:')[1].strip())
                    except ValueError:
                        pass
                elif 'Correct Answers:' in line:
                    try:
                        correct_answers = int(line.split('Correct Answers:')[1].strip())
                    except ValueError:
                        pass

            return {
                'method': method,
                'dataset': dataset,
                'accuracy': accuracy,
                'total_problems': total_problems,
                'answered_problems': answered_problems,
                'correct_answers': correct_answers,
                'status': 'success',
                'eval_dir': method_eval_dir
            }
        else:
            error_msg = result.stderr
            print(f"❌ Evaluation failed for {method}/{dataset}: {error_msg}")
            return {
                'method': method,
                'dataset': dataset,
                'accuracy': None,
                'status': 'failed',
                'error': error_msg,
                'eval_dir': method_eval_dir
            }
            
    except Exception as e:
        print(f"❌ Exception during evaluation of {method}/{dataset}: {e}")
        return {
            'method': method,
            'dataset': dataset,
            'accuracy': None,
            'status': 'error',
            'error': str(e),
            'eval_dir': method_eval_dir
        }


def main():
    parser = argparse.ArgumentParser(description="Evaluate baseline results")
    parser.add_argument("--baseline-dir", type=str, default="results_baseline",
                       help="Directory containing baseline results")
    parser.add_argument("--eval-output-dir", type=str, default="results_baseline/evaluation",
                       help="Directory to store evaluation outputs")
    parser.add_argument("--method", choices=BASELINE_METHODS + ['all'],
                       default='all', help="Baseline method to evaluate")
    parser.add_argument("--dataset", choices=BENCHMARK_DATASETS + ['all'],
                       default='all', help="Dataset to evaluate")
    parser.add_argument("--summary", action='store_true',
                       help="Show summary table at the end")
    
    args = parser.parse_args()

    # Determine which experiments to evaluate
    methods = BASELINE_METHODS if args.method == 'all' else [args.method]
    datasets = BENCHMARK_DATASETS if args.dataset == 'all' else [args.dataset]

    # Find available baseline results
    available_combinations = []
    for method in methods:
        method_dir = os.path.join(args.baseline_dir, method)
        if os.path.exists(method_dir):
            for dataset in datasets:
                result_file = os.path.join(method_dir, f"{dataset}_result_eval_format.json")
                if os.path.exists(result_file):
                    available_combinations.append((method, dataset))

    if not available_combinations:
        print("❌ No baseline results found to evaluate!")
        return
    
    # Run evaluations
    eval_results = []
    for method, dataset in available_combinations:
        result = run_evaluation_for_method(method, dataset, args.baseline_dir, args.eval_output_dir)
        if result:
            eval_results.append(result)
    
    # Print results summary
    successful_evals = [r for r in eval_results if r['status'] == 'success' and r['accuracy'] is not None]

    # Always print detailed results
    if successful_evals:
        print(f"\n📊 Detailed Evaluation Results:")
        print(f"{'Method':<12} | {'Dataset':<12} | {'Accuracy':<8} | {'Problems':<12} | {'Details'}")
        print("-" * 85)
        for result in successful_evals:
            total = result.get('total_problems', 'N/A')
            answered = result.get('answered_problems', 'N/A')
            correct = result.get('correct_answers', 'N/A')

            if total != 'N/A' and answered != 'N/A' and correct != 'N/A':
                problems_info = f"{answered}/{total}"
                details = f"{correct}/{answered} correct"
            else:
                problems_info = "N/A"
                details = "N/A"

            print(f"{result['method']:<12} | {result['dataset']:<12} | {result['accuracy']:>7.2f}% | {problems_info:<12} | {details}")

    if successful_evals and args.summary:
        print(f"\n📋 Summary Table:")
        # Group by dataset
        dataset_results = {}
        for result in successful_evals:
            dataset = result['dataset']
            if dataset not in dataset_results:
                dataset_results[dataset] = {}
            dataset_results[dataset][result['method']] = result['accuracy']

        # Print table header
        header = f"{'Dataset':<12}"
        for method in BASELINE_METHODS:
            header += f" | {method:>10}"
        print(header)
        print("-" * 60)

        # Print table rows
        for dataset in sorted(dataset_results.keys()):
            row = f"{dataset:<12}"
            for method in BASELINE_METHODS:
                if method in dataset_results[dataset]:
                    accuracy = dataset_results[dataset][method]
                    row += f" | {accuracy:>8.2f}%"
                else:
                    row += f" | {'N/A':>10}"
            print(row)

        # Calculate average across datasets for each method
        print("-" * 60)
        avg_row = f"{'Average':<12}"
        for method in BASELINE_METHODS:
            method_accuracies = [dataset_results[d][method] for d in dataset_results if method in dataset_results[d]]
            if method_accuracies:
                avg_accuracy = sum(method_accuracies) / len(method_accuracies)
                avg_row += f" | {avg_accuracy:>8.2f}%"
            else:
                avg_row += f" | {'N/A':>10}"
        print(avg_row)
    elif successful_evals:
        # Just show individual results if no summary requested
        for result in successful_evals:
            print(f"{result['method']:<10} | {result['dataset']:<12} | {result['accuracy']:>6.2f}%")


if __name__ == "__main__":
    main()