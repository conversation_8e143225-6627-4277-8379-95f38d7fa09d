#!/usr/bin/env python3
"""
Comprehensive evaluation script for all methods (baselines + our methods)
Extended from evaluate_baselines.py to include our collaborative methods
"""

import argparse
import os
import csv
import sys
import json
from pathlib import Path
from typing import Dict, List, Optional

try:
    import pandas as pd
except ImportError:
    print("Warning: pandas not available, some features may not work")
    pd = None

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# All methods to evaluate
BASELINE_METHODS = ["direct_io", "cot", "cot_sc", "cod", "llm_debate"]
OUR_METHODS = ["simplified_collaborative"]
ALL_METHODS = BASELINE_METHODS + OUR_METHODS

DATASETS = ["drop", "gpqa", "gsm8k", "hotpotqa", "humaneval", "math", "mbpp", "mmlu", "strategyqa"]

# 删除有问题的手动评估函数，改用主evaluator的结果

def load_evaluation_results(method: str, dataset: str) -> Optional[Dict]:
    """从主evaluator生成的CSV文件中加载评估结果"""
    try:
        # 尝试多种可能的文件名格式，优先使用新生成的 eval_format 文件
        possible_file_patterns = [
            f"results_baseline/evaluation/{method}/{dataset}_result_eval_format_{dataset.upper()}.csv",
            f"results_baseline/evaluation/{method}/{dataset}_result_eval_format_{dataset}.csv",
            f"results_baseline/evaluation/{method}/{dataset}_result_{dataset.upper()}.csv",
            f"results_baseline/evaluation/{method}/{dataset}_result_{dataset}.csv"
        ]

        csv_file = None
        for pattern in possible_file_patterns:
            if os.path.exists(pattern):
                csv_file = pattern
                break

        if csv_file is None:
            return None

        # 使用csv模块读取
        with open(csv_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            rows = list(reader)

        if len(rows) == 0:
            return None

        # 计算准确率
        if 'score' in rows[0]:
            correct = sum(1 for row in rows if float(row.get('score', 0)) >= 1.0)
            total = len(rows)
            accuracy = (correct / total) * 100 if total > 0 else 0

            return {
                'method': method,
                'dataset': dataset,
                'accuracy': accuracy,
                'correct': correct,
                'total': total
            }

        return None

    except Exception as e:
        print(f"Error loading evaluation results for {method}/{dataset}: {e}")
        return None

def evaluate_baseline_method(baseline_dir: str, eval_output_dir: str, method: str, dataset: str) -> Optional[Dict]:
    """从主evaluator生成的结果中获取评估数据"""
    # 首先尝试从CSV结果文件中读取（主evaluator生成的）
    result = load_evaluation_results(method, dataset)
    if result:
        return result

    # 如果没有CSV结果，说明主evaluator还没有运行，返回None
    return None

def evaluate_our_method(our_results_dir: str, method: str, dataset: str) -> Optional[Dict]:
    """Evaluate our method for a specific dataset"""
    eval_file = Path(our_results_dir) / "benchmark_outputs" / "evaluation_logs" / dataset / f"{dataset}_result_answers_{dataset}.csv"
    
    if not eval_file.exists():
        return None
    
    try:
        with open(eval_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            rows = list(reader)
            
        if not rows:
            return None
            
        # Count correct answers (score >= 1.0)
        correct = sum(1 for row in rows if float(row.get('score', 0)) >= 1.0)
        total = len(rows)
        accuracy = (correct / total) * 100 if total > 0 else 0
        
        return {
            'method': method,
            'dataset': dataset,
            'accuracy': accuracy,
            'correct': correct,
            'total': total
        }
    except Exception as e:
        print(f"Error evaluating our method {method} on {dataset}: {e}")
        return None

def check_available_results(baseline_dir: str) -> Dict[str, List[str]]:
    """检查哪些方法和数据集有可用的结果文件"""
    available = {}

    for method in BASELINE_METHODS:
        method_dir = os.path.join(baseline_dir, method)
        if os.path.exists(method_dir):
            available[method] = []
            for dataset in DATASETS:
                result_file = os.path.join(method_dir, f"{dataset}_result.json")
                eval_format_file = os.path.join(method_dir, f"{dataset}_result_eval_format.json")
                if os.path.exists(result_file) or os.path.exists(eval_format_file):
                    available[method].append(dataset)

    return available

def main():
    parser = argparse.ArgumentParser(description="Comprehensive evaluation of all methods")
    parser.add_argument("--baseline-dir", required=True, help="Directory containing baseline results")
    parser.add_argument("--eval-output-dir", required=True, help="Directory for evaluation outputs")
    parser.add_argument("--our-results-dir", required=True, help="Directory containing our method results")
    parser.add_argument("--method", default="all", help="Method to evaluate (all, baseline, our, or specific method)")
    parser.add_argument("--dataset", default="all", help="Dataset to evaluate (all or specific dataset)")
    parser.add_argument("--summary", action="store_true", help="Show summary table")
    parser.add_argument("--show-available", action="store_true", help="Show available result files")

    args = parser.parse_args()

    # Check available results
    available_results = check_available_results(args.baseline_dir)

    if args.show_available:
        print("📁 Available Result Files:")
        for method, datasets in available_results.items():
            if datasets:
                print(f"  {method}: {', '.join(datasets)} ({len(datasets)} datasets)")
            else:
                print(f"  {method}: No results found")
        print()
    
    # Determine which methods to evaluate
    if args.method == "all":
        methods_to_eval = ALL_METHODS
    elif args.method == "baseline":
        methods_to_eval = BASELINE_METHODS
    elif args.method == "our":
        methods_to_eval = OUR_METHODS
    elif args.method in ALL_METHODS:
        methods_to_eval = [args.method]
    else:
        print(f"Unknown method: {args.method}")
        return
    
    # Determine which datasets to evaluate
    if args.dataset == "all":
        datasets_to_eval = DATASETS
    elif args.dataset in DATASETS:
        datasets_to_eval = [args.dataset]
    else:
        print(f"Unknown dataset: {args.dataset}")
        return
    
    # Collect all results
    all_results = []
    dataset_results = {}
    
    for dataset in datasets_to_eval:
        dataset_results[dataset] = {}
        
        for method in methods_to_eval:
            if method in BASELINE_METHODS:
                result = evaluate_baseline_method(args.baseline_dir, args.eval_output_dir, method, dataset)
            elif method in OUR_METHODS:
                result = evaluate_our_method(args.our_results_dir, method, dataset)
            else:
                continue
                
            if result:
                all_results.append(result)
                dataset_results[dataset][method] = result['accuracy']
    
    # Display detailed results first
    successful_evals = [r for r in all_results if r is not None]

    if successful_evals:
        print(f"\n📊 Detailed Evaluation Results:")
        print(f"{'Method':<12} | {'Dataset':<12} | {'Accuracy':<8} | {'Problems':<12}")
        print("-" * 60)
        for result in successful_evals:
            total = result.get('total', 'N/A')
            correct = result.get('correct', 'N/A')

            if total != 'N/A' and correct != 'N/A':
                problems_info = f"{correct}/{total}"
            else:
                problems_info = "N/A"

            print(f"{result['method']:<12} | {result['dataset']:<12} | {result['accuracy']:>7.2f}% | {problems_info:<12}")

    if args.summary and successful_evals:
        print(f"\n📋 Summary Comparison Table:")
        # Show summary table
        print("Dataset      |  direct_io |        cot |     cot_sc |        cod | llm_debate | our_method")
        print("-" * 88)
        
        for dataset in datasets_to_eval:
            if dataset in dataset_results:
                row = f"{dataset:<12}"
                
                # Baseline methods
                for method in BASELINE_METHODS:
                    if method in dataset_results[dataset]:
                        accuracy = dataset_results[dataset][method]
                        row += f" | {accuracy:>8.2f}%"
                    else:
                        row += f" | {'N/A':>10}"
                
                # Our methods
                for method in OUR_METHODS:
                    if method in dataset_results[dataset]:
                        accuracy = dataset_results[dataset][method]
                        row += f" | {accuracy:>8.2f}%"
                    else:
                        row += f" | {'N/A':>10}"
                
                print(row)
        
        # Calculate average across datasets for each method
        print("-" * 88)
        avg_row = f"{'Average':<12}"
        
        for method in ALL_METHODS:
            method_accuracies = [dataset_results[d][method] for d in dataset_results if method in dataset_results[d]]
            if method_accuracies:
                avg_accuracy = sum(method_accuracies) / len(method_accuracies)
                avg_row += f" | {avg_accuracy:>8.2f}%"
            else:
                avg_row += f" | {'N/A':>10}"
        
        print(avg_row)
        
    elif successful_evals:
        # Just show individual results if no summary requested
        for result in successful_evals:
            print(f"{result['method']:<15} | {result['dataset']:<12} | {result['accuracy']:>6.2f}%")

if __name__ == "__main__":
    main()
