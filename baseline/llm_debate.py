#!/usr/bin/env python3
"""
LLM-Debate Baseline
Multi-agent debate system where multiple agents discuss and reach consensus on answers
"""

import asyncio
from typing import Dict, Any, List, Optional
from .base_baseline import BaseBaseline
from utils.api import async_generate_completion


class LLMDebateBaseline(BaseBaseline):
    """LLM-Debate - uses multi-agent debate to reach consensus on answers"""
    
    def __init__(self, temperature: float = 0.7, num_agents: int = 3, max_rounds: int = 2, model: str = "openai"):
        super().__init__(temperature, model)
        self.num_agents = num_agents
        self.max_rounds = max_rounds
    
    def get_method_name(self) -> str:
        return "llm_debate"
    
    async def generate_answer(self, problem: Dict[str, Any]) -> str:
        """Generate answer using multi-agent debate"""
        
        # Extract question from different possible keys
        # Handle different dataset formats
        if 'context' in problem and isinstance(problem['context'], list):
            # HotpotQA format - build context + question
            context = problem['context']
            context_text = ""
            for ctx in context:
                if isinstance(ctx, list) and len(ctx) >= 2:
                    title = ctx[0]
                    paragraphs = ctx[1]
                    if isinstance(paragraphs, list):
                        context_text += f"\n{title}:\n" + " ".join(paragraphs) + "\n"
                    else:
                        context_text += f"\n{title}:\n{paragraphs}\n"
            question = f"Context: {context_text.strip()}\n\nQuestion: {problem.get('question', '')}"
        elif 'context' in problem:
            # DROP dataset format: context contains passage + question
            question = problem.get('context', '')
        else:
            # Standard format: prompt, question, or problem fields
            question = problem.get('prompt') or problem.get('question') or problem.get('problem') or ''

        # Handle case where question might be a list (e.g., hotpotqa)
        if isinstance(question, list):
            question = ' '.join(str(x) for x in question)
        elif not isinstance(question, str):
            question = str(question)
        
        if not question.strip():
            return "Error: No question content found"
        
        try:
            # Simple multi-agent debate without complex file system
            result = await self._simple_multi_agent_debate(question)
            return result

        except Exception as e:
            print(f"Error in LLM-Debate: {e}")
            return f"Error: {str(e)}"
    
    async def _simple_multi_agent_debate(self, question: str) -> str:
        """Simple multi-agent debate without file system complexity"""

        # Get available agents
        available_agents = self._get_available_agents()[:self.num_agents]

        # Round 1: Each agent generates initial answer
        initial_answers = []
        for agent in available_agents:
            try:
                answer = await self._generate_agent_answer(agent, question)
                if answer:
                    initial_answers.append({"agent": agent, "answer": answer})
            except Exception as e:
                print(f"Agent {agent} failed: {e}")

        if not initial_answers:
            return "Error: No agents could generate answers"

        # Round 2: Debate and consensus
        if len(initial_answers) > 1:
            final_answer = await self._debate_and_consensus(question, initial_answers)
        else:
            final_answer = initial_answers[0]["answer"]

        # Return the complete reasoning process, let evaluator extract the answer
        return final_answer
    
    def _get_available_agents(self) -> List[str]:
        """Get list of available agents based on current model"""
        
        # Map model to available agents (openai quota restored)
        model_to_agents = {
            "openai": ["openai", "gemini", "llama"],
            "anthropic": ["anthropic", "openai", "gemini"],
            "deepseek": ["deepseek", "openai", "gemini"],
            "qwen": ["qwen", "openai", "gemini"],
            "llama": ["llama", "openai", "gemini"],
            "gemini": ["gemini", "openai", "llama"]
        }
        
        # Get agents for current model, fallback to default
        agents = model_to_agents.get(self.model, ["openai", "gemini", "llama"])
        
        return agents

    async def _generate_agent_answer(self, agent_id: str, question: str) -> str:
        """Generate answer from a single agent with token limit"""

        prompt = f"""Please solve this problem step by step in MAX 80 TOKENS:

{question}

Provide a concise, step-by-step solution and end with your final answer.
Keep your response under 80 tokens for efficiency."""

        try:
            response = await async_generate_completion(
                agent_id=agent_id,
                prompt=prompt,
                max_tokens=512,
                temperature=self.temperature
            )
            return response.strip()
        except Exception as e:
            print(f"Agent {agent_id} generation failed: {e}")
            return ""

    async def _debate_and_consensus(self, question: str, initial_answers: List[Dict]) -> str:
        """Simple debate and consensus mechanism"""

        # Create debate prompt with all initial answers
        answers_text = "\n\n".join([
            f"Agent {ans['agent']}:\n{ans['answer']}"
            for ans in initial_answers
        ])

        debate_prompt = f"""Question: {question}

Here are different solutions from multiple agents:

{answers_text}

Analyze these solutions and declare the best one. In one sentence, declare WINNER_A, WINNER_B, or WINNER_C. 
Then provide a 20-token rationale. Finally, give the consensus answer.

For math problems, end your response with:
#### [number]

For yes/no questions, end with:
#### Yes/No

Focus on accuracy and clarity."""

        # Use the primary model for final consensus
        try:
            consensus = await async_generate_completion(
                agent_id=self.model,
                prompt=debate_prompt,
                max_tokens=512,
                temperature=0.3  # Lower temperature for consensus
            )
            return consensus.strip()
        except Exception as e:
            print(f"Consensus generation failed: {e}")
            # Fallback to first answer
            return initial_answers[0]["answer"]
    

