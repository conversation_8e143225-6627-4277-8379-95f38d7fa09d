#!/usr/bin/env python3
"""
Chain of Thought (CoT) Baseline
Prompts the model to think step by step before answering
"""

from typing import Dict, Any
from .base_baseline import BaseBaseline


class CoTBaseline(BaseBaseline):
    """Chain of Thought baseline - encourages step-by-step reasoning"""
    
    def get_method_name(self) -> str:
        return "cot"
    
    async def generate_answer(self, problem: Dict[str, Any]) -> str:
        """Generate answer with step-by-step reasoning"""
        
        # Extract question from different possible keys
        # Handle different dataset formats
        if 'context' in problem and isinstance(problem['context'], list):
            # HotpotQA format - build context + question
            context = problem['context']
            context_text = ""
            for ctx in context:
                if isinstance(ctx, list) and len(ctx) >= 2:
                    title = ctx[0]
                    paragraphs = ctx[1]
                    if isinstance(paragraphs, list):
                        context_text += f"\n{title}:\n" + " ".join(paragraphs) + "\n"
                    else:
                        context_text += f"\n{title}:\n{paragraphs}\n"
            question = f"Context: {context_text.strip()}\n\nQuestion: {problem.get('question', '')}"
        elif 'context' in problem:
            # DROP dataset format: context contains passage + question
            question = problem.get('context', '')
        else:
            # Standard format: prompt, question, or problem fields
            question = problem.get('prompt') or problem.get('question') or problem.get('problem') or ''

        # Handle case where question might be a list (e.g., hotpotqa)
        if isinstance(question, list):
            question = ' '.join(str(x) for x in question)
        elif not isinstance(question, str):
            question = str(question)
        
        # Create Chain of Thought prompt with clear structure
        prompt = f"""Please solve the following problem step by step. First output your reasoning, then provide the final answer.

Question: {question}

Let's think step by step. First output your reasoning. 
When you are completely done with reasoning, on a new line write:
FINAL ANSWER: <answer>

For math problems, use this format:
FINAL ANSWER: #### [number]

For yes/no questions, use:
FINAL ANSWER: Yes/No [brief explanation]

For multiple choice, use:
FINAL ANSWER: [Letter]"""
        
        # Get answer from API with higher temperature for more diverse reasoning
        answer = await self._api_call(prompt, temperature=0.7)
        return answer