#!/usr/bin/env python3
"""
Chain of Thought with Self-Consistency (CoT-SC) Baseline
Generates multiple reasoning paths and selects the most consistent answer
"""

import asyncio
from collections import Counter
from typing import Dict, Any, List
from .base_baseline import BaseBaseline


class CoTSCBaseline(BaseBaseline):
    """Chain of Thought with Self-Consistency - generates multiple solutions and picks most frequent"""
    
    def __init__(self, temperature: float = 0.7, num_samples: int = 5, model: str = "openai"):
        super().__init__(temperature, model)
        self.num_samples = num_samples
    
    def get_method_name(self) -> str:
        return "cot_sc"
    
    async def generate_answer(self, problem: Dict[str, Any]) -> str:
        """Generate multiple CoT solutions and select most consistent answer"""
        
        # Extract question from different possible keys
        # Handle different dataset formats
        if 'context' in problem and isinstance(problem['context'], list):
            # HotpotQA format - build context + question
            context = problem['context']
            context_text = ""
            for ctx in context:
                if isinstance(ctx, list) and len(ctx) >= 2:
                    title = ctx[0]
                    paragraphs = ctx[1]
                    if isinstance(paragraphs, list):
                        context_text += f"\n{title}:\n" + " ".join(paragraphs) + "\n"
                    else:
                        context_text += f"\n{title}:\n{paragraphs}\n"
            question = f"Context: {context_text.strip()}\n\nQuestion: {problem.get('question', '')}"
        elif 'context' in problem:
            # DROP dataset format: context contains passage + question
            question = problem.get('context', '')
        else:
            # Standard format: prompt, question, or problem fields
            question = problem.get('prompt') or problem.get('question') or problem.get('problem') or ''

        # Handle case where question might be a list (e.g., hotpotqa)
        if isinstance(question, list):
            question = ' '.join(str(x) for x in question)
        elif not isinstance(question, str):
            question = str(question)
        
        # Generate multiple reasoning paths
        solutions = await self._generate_multiple_solutions(question)
        
        # Extract final answers from each solution
        final_answers = []
        for solution in solutions:
            final_answer = self._extract_final_answer(solution)
            if final_answer:
                final_answers.append(final_answer)
        
        # Use majority voting for final answer
        if final_answers:
            most_common = Counter(final_answers).most_common(1)[0][0]
            
            # Return the full solution that produced the most common answer
            for solution in solutions:
                if self._extract_final_answer(solution) == most_common:
                    return f"[Self-Consistency with {len(final_answers)} samples]\n\n{solution}"
        
        # Fallback: return the first solution if no consensus
        return solutions[0] if solutions else "Error: No valid solutions generated"
    
    async def _generate_multiple_solutions(self, question: str) -> List[str]:
        """Generate multiple CoT solutions concurrently"""
        
        prompt = f"""Provide a concise but deterministic chain of thought. Solve the following problem step by step.

Question: {question}

Let's think step by step. When you are completely done with reasoning, on a new line write:
FINAL ANSWER: <answer>

For math problems, use this format:
FINAL ANSWER: #### [number]

For yes/no questions, use:
FINAL ANSWER: Yes/No [brief explanation]

For multiple choice, use:
FINAL ANSWER: [Letter]"""
        
        # Generate solutions concurrently
        tasks = []
        for _ in range(self.num_samples):
            task = self._api_call(prompt, temperature=self.temperature)
            tasks.append(task)
        
        solutions = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Filter out failed solutions
        valid_solutions = []
        for solution in solutions:
            if isinstance(solution, str) and solution.strip():
                valid_solutions.append(solution)
            elif isinstance(solution, Exception):
                print(f"Warning: Solution generation failed: {solution}")
        
        return valid_solutions
    
    def _extract_final_answer(self, solution: str) -> str:
        """Extract the final answer from a solution with improved patterns"""
        solution = solution.strip()
        
        import re
        
        # Priority 1: Look for FINAL ANSWER: pattern
        final_answer_pattern = r"FINAL ANSWER:\s*(.+)"
        match = re.search(final_answer_pattern, solution, re.IGNORECASE)
        if match:
            return match.group(1).strip()
        
        # Priority 2: Look for #### pattern (math problems)
        math_pattern = r"####\s*([0-9.,\-+*/\s]+)"
        match = re.search(math_pattern, solution)
        if match:
            return match.group(1).strip()
        
        # Priority 3: Look for common answer patterns
        answer_patterns = [
            r"(?:final answer|answer|therefore|thus|so)\s*:?\s*([^.\n]+)",
            r"(?:the answer is|answer is)\s*:?\s*([^.\n]+)",
            r"\$\$([^$]+)\$\$",  # LaTeX math
            r"```\s*([^`]+)\s*```",  # Code blocks
        ]
        
        for pattern in answer_patterns:
            matches = re.findall(pattern, solution, re.IGNORECASE)
            if matches:
                return matches[-1].strip()  # Return last match
        
        # If no pattern matches, return last non-empty line
        lines = [line.strip() for line in solution.split('\n') if line.strip()]
        if lines:
            return lines[-1]
        
        return solution[:100]  # Fallback to first 100 chars