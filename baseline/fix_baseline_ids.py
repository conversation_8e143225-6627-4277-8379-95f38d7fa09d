#!/usr/bin/env python3
"""
Fix baseline result IDs to match benchmark data IDs
"""

import json
import os
from pathlib import Path

def load_benchmark_ids(dataset: str):
    """Load benchmark IDs for a dataset"""
    benchmark_file = f"benchmark/{dataset}.jsonl"
    ids = []
    
    with open(benchmark_file, 'r', encoding='utf-8') as f:
        for line in f:
            if line.strip():
                item = json.loads(line.strip())
                
                # Different datasets use different ID fields
                if dataset == "hotpotqa":
                    ids.append(item["_id"])
                elif dataset in ["humaneval", "mbpp"]:
                    ids.append(item["task_id"])
                else:
                    ids.append(item["id"])
    
    return ids

def fix_baseline_results(method: str, dataset: str):
    """Fix IDs in baseline results to match benchmark"""
    result_file = f"results_baseline/{method}/{dataset}_result.json"
    
    if not os.path.exists(result_file):
        print(f"Skipping {method}/{dataset} - file not found")
        return
    
    # Load benchmark IDs
    try:
        benchmark_ids = load_benchmark_ids(dataset)
    except Exception as e:
        print(f"Error loading benchmark IDs for {dataset}: {e}")
        return
    
    # Load baseline results
    with open(result_file, 'r', encoding='utf-8') as f:
        results = json.load(f)
    
    # Fix IDs
    fixed_results = []
    for i, result in enumerate(results):
        if i < len(benchmark_ids):
            # Update ID to match benchmark
            benchmark_id = benchmark_ids[i]

            # For HotpotQA, we need to use _id field in evaluator matching
            if dataset == "hotpotqa":
                result["_id"] = benchmark_id
                result["id"] = benchmark_id  # Keep both for compatibility
            elif dataset in ["humaneval", "mbpp"]:
                result["task_id"] = benchmark_id
                result["id"] = benchmark_id  # Keep both for compatibility
            else:
                result["id"] = benchmark_id

            fixed_results.append(result)
        else:
            print(f"Warning: {method}/{dataset} has more results than benchmark problems")
            break
    
    # Save fixed results
    with open(result_file, 'w', encoding='utf-8') as f:
        json.dump(fixed_results, f, indent=2, ensure_ascii=False)
    
    print(f"Fixed {len(fixed_results)} IDs for {method}/{dataset}")

def main():
    """Fix all baseline result IDs"""
    methods = ["direct_io", "cot", "cot_sc", "cod", "llm_debate"]
    datasets = ["drop", "hotpotqa", "humaneval", "mbpp"]  # Only datasets with ID issues
    
    print("Fixing baseline result IDs to match benchmark data...")
    
    for method in methods:
        for dataset in datasets:
            fix_baseline_results(method, dataset)
    
    print("ID fixing completed!")

if __name__ == "__main__":
    main()
