#!/usr/bin/env python3
"""
Baseline Methods Runner
Runs all baseline methods on benchmark datasets
"""

import asyncio
import argparse
import os
import sys
from typing import List, Dict, Any

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from baseline.direct_io import DirectIOBaseline
from baseline.cot import CoTBaseline
from baseline.cot_sc import CoTSCBaseline
from baseline.cod import CoDBaseline
from baseline.llm_debate import LLMDebateBaseline


BASELINE_METHODS = {
    'direct_io': DirectIOBaseline,
    'cot': CoTBaseline,
    'cot_sc': CoTSCBaseline,
    'cod': CoDBaseline,
    'llm_debate': LLMDebateBaseline
}

BENCHMARK_DATASETS = [
    'gsm8k', 'math', 'mbpp', 'humaneval',
    'drop', 'hotpotqa', 'strategyqa', 'gpqa', 'mmlu'
]

# Available models for baseline experiments
AVAILABLE_MODELS = ["openai", "anthropic", "deepseek", "qwen", "llama", "gemini"]
DEFAULT_MODEL = "openai"  # Default model if none specified


async def run_baseline_experiment(
    method_name: str,
    dataset: str,
    max_problems: int = 50,
    output_dir: str = None,
    model: str = DEFAULT_MODEL
):
    """Run a single baseline experiment"""

    if method_name not in BASELINE_METHODS:
        raise ValueError(f"Unknown method: {method_name}")

    if model not in AVAILABLE_MODELS:
        raise ValueError(f"Unknown model: {model}. Available models: {AVAILABLE_MODELS}")

    # Initialize baseline method with specified model
    method_class = BASELINE_METHODS[method_name]
    if method_name == 'cot_sc':
        baseline = method_class(num_samples=5, model=model)
    elif method_name == 'llm_debate':
        baseline = method_class(num_agents=3, max_rounds=3, model=model)
    else:
        baseline = method_class(model=model)
    
    # Set up paths - each method gets its own folder (no model grouping)
    benchmark_file = f"benchmark/{dataset}.jsonl"
    if output_dir is None:
        output_dir = f"results_baseline/{method_name}"
    else:
        output_dir = f"{output_dir}/{method_name}"

    os.makedirs(output_dir, exist_ok=True)
    output_file = f"{output_dir}/{dataset}_result.json"
    
    # Run experiment
    try:
        result = await baseline.process_dataset(
            jsonl_file=benchmark_file,
            max_problems=max_problems,
            output_file=output_file
        )
        
        return result

    except Exception as e:
        print(f"❌ Failed {method_name}/{dataset} with {model}: {e}")
        return None


async def main():
    parser = argparse.ArgumentParser(description="Run baseline experiments")
    parser.add_argument("--method", choices=list(BASELINE_METHODS.keys()) + ['all'],
                       default='all', help="Baseline method to run")
    parser.add_argument("--dataset", choices=BENCHMARK_DATASETS + ['all'],
                       default='all', help="Dataset to process")
    parser.add_argument("--model", choices=AVAILABLE_MODELS,
                       default=DEFAULT_MODEL, help=f"Model to use for experiments (default: {DEFAULT_MODEL})")
    parser.add_argument("--max-problems", type=int, default=50,
                       help="Maximum number of problems to process per dataset")
    parser.add_argument("--output-dir", type=str, default=None,
                       help="Output directory for results")
    parser.add_argument("--parallel", action='store_true',
                       help="Run experiments in parallel")
    
    args = parser.parse_args()

    # Determine which experiments to run
    methods = list(BASELINE_METHODS.keys()) if args.method == 'all' else [args.method]
    datasets = BENCHMARK_DATASETS if args.dataset == 'all' else [args.dataset]

    print(f"🚀 Running baseline experiments with model: {args.model}")
    print(f"Methods: {methods}")
    print(f"Datasets: {datasets}")
    print(f"Max problems per dataset: {args.max_problems}")
    print("")

    # Create experiment tasks
    tasks = []
    for method in methods:
        for dataset in datasets:
            task = run_baseline_experiment(
                method_name=method,
                dataset=dataset,
                max_problems=args.max_problems,
                output_dir=args.output_dir,
                model=args.model
            )
            tasks.append((method, dataset, task))
    
    # Run experiments
    if args.parallel:
        print("⚡ Running experiments in parallel...")
        # Run all tasks concurrently
        results = await asyncio.gather(*[task for _, _, task in tasks], return_exceptions=True)

        # Report results
        success_count = sum(1 for result in results if result is not None and not isinstance(result, Exception))
        total_count = len(results)
        print(f"\n🏁 Parallel execution completed: {success_count}/{total_count} experiments successful")
    else:
        print("🔄 Running experiments sequentially...")
        # Run tasks sequentially
        success_count = 0
        for i, (method, dataset, task) in enumerate(tasks, 1):
            print(f"📊 Progress: {i}/{len(tasks)} - Running {method}/{dataset}")
            result = await task
            if result is not None:
                success_count += 1
                print(f"✅ {method}/{dataset} completed successfully")
            else:
                print(f"❌ {method}/{dataset} failed")

        print(f"\n🏁 Sequential execution completed: {success_count}/{len(tasks)} experiments successful")


if __name__ == "__main__":
    asyncio.run(main())