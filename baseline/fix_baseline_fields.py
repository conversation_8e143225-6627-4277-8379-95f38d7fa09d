#!/usr/bin/env python3
"""
Fix baseline result field names to match evaluator expectations
"""

import json
import os

def fix_baseline_fields(method: str, dataset: str):
    """Fix field names in baseline results"""
    result_file = f"results_baseline/{method}/{dataset}_result.json"
    
    if not os.path.exists(result_file):
        print(f"Skipping {method}/{dataset} - file not found")
        return
    
    # Load baseline results
    with open(result_file, 'r', encoding='utf-8') as f:
        results = json.load(f)
    
    # Fix field names based on dataset requirements
    fixed_results = []
    for result in results:
        # Copy all existing fields
        fixed_result = result.copy()
        
        # Add prompt field for datasets that expect it
        if dataset in ["hotpotqa", "humaneval", "mbpp"]:
            if "question" in result and "prompt" not in result:
                fixed_result["prompt"] = result["question"]
        
        # Add solution field for datasets that expect it
        if "answer" in result and "solution" not in result:
            fixed_result["solution"] = result["answer"]
        
        fixed_results.append(fixed_result)
    
    # Save fixed results
    with open(result_file, 'w', encoding='utf-8') as f:
        json.dump(fixed_results, f, indent=2, ensure_ascii=False)
    
    print(f"Fixed fields for {method}/{dataset}")

def main():
    """Fix all baseline result fields"""
    methods = ["direct_io", "cot", "cot_sc", "cod", "llm_debate"]
    datasets = ["drop", "hotpotqa", "humaneval", "mbpp", "gpqa", "gsm8k", "math", "mmlu", "strategyqa"]
    
    print("Fixing baseline result fields...")
    
    for method in methods:
        for dataset in datasets:
            fix_baseline_fields(method, dataset)
    
    print("Field fixing completed!")

if __name__ == "__main__":
    main()
