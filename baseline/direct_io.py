#!/usr/bin/env python3
"""
Direct I/O Baseline
Simple direct input-output without any reasoning steps
"""

from typing import Dict, Any
from .base_baseline import BaseBaseline
import os


class DirectIOBaseline(BaseBaseline):
    """Direct I/O baseline - simple question answering without reasoning"""
    
    def get_method_name(self) -> str:
        return "direct_io"
    
    def _detect_dataset_type(self, problem: Dict[str, Any]) -> str:
        """Detect dataset type based on problem structure and file context"""
        # Try to get dataset type from filename if available
        if hasattr(self, 'current_file') and self.current_file:
            filename = os.path.basename(self.current_file)
            if filename.startswith('gsm8k'):
                return 'gsm8k'
            elif filename.startswith('math'):
                return 'math'
            elif filename.startswith('mbpp'):
                return 'mbpp'
            elif filename.startswith('humaneval'):
                return 'humaneval'
            elif filename.startswith('drop'):
                return 'drop'
            elif filename.startswith('hotpotqa'):
                return 'hotpotqa'
            elif filename.startswith('strategyqa'):
                return 'strategyqa'
            elif filename.startswith('gpqa'):
                return 'gpqa'
            elif filename.startswith('mmlu'):
                return 'mmlu'
        
        # Fallback: detect based on problem structure
        if 'context' in problem and isinstance(problem['context'], list):
            return 'hotpotqa'
        elif 'context' in problem:
            return 'drop'
        elif 'prompt' in problem and 'def ' in str(problem.get('prompt', '')):
            return 'humaneval'
        elif 'question' in problem and '?' in str(problem.get('question', '')):
            return 'strategyqa' if 'yes' in str(problem.get('question', '')).lower() or 'no' in str(problem.get('question', '')).lower() else 'gsm8k'
        
        return 'gsm8k'  # Default fallback
    
    def _get_specialized_prompt(self, dataset_type: str, question: str) -> str:
        """Generate specialized prompt based on dataset type"""
        
        if dataset_type == 'gsm8k':
            return f"""Solve this grade school math problem step by step with careful calculation.

Question: {question}

Instructions:
1. Identify all numbers and operations needed
2. Work through calculations systematically step by step
3. Double-check each arithmetic operation
4. Format your final answer as: #### [number]

Let me solve this step by step:"""

        elif dataset_type == 'math':
            return f"""Solve this advanced mathematics problem using rigorous mathematical reasoning.

Problem: {question}

Approach:
1. Identify the mathematical domain and relevant theorems or formulas
2. Set up equations or relationships systematically
3. Apply appropriate mathematical techniques step-by-step
4. Verify the solution satisfies all conditions
5. Express the answer in its simplest form

Answer: #### [answer]"""

        elif dataset_type == 'mbpp':
            return f"""Write a Python function to solve this programming problem.

Problem: {question}

Requirements:
1. Implement the function exactly as specified
2. Consider edge cases and input validation
3. Write clean, efficient code following Python conventions
4. Provide only the complete function code without extra formatting

def """

        elif dataset_type == 'humaneval':
            return f"""Complete the following Python function.

{question}
"""

        elif dataset_type == 'drop':
            return f"""Answer this reading comprehension question by carefully analyzing the passage.

{question}

Instructions:
1. Locate all relevant information in the passage
2. Identify any numerical data or relationships needed
3. Perform any required calculations step by step
4. Extract the precise answer to the question
5. Format as: #### [answer]

Answer:"""

        elif dataset_type == 'hotpotqa':
            return f"""Answer this question by connecting information from the provided sources.

{question}

Multi-hop reasoning approach:
1. Identify the key entities and relationships in the question
2. Find relevant information for each entity across all sources
3. Connect the information to form a logical reasoning chain
4. Provide the most specific, concise answer possible

Answer:"""

        elif dataset_type == 'strategyqa':
            return f"""Answer this yes/no question using logical reasoning and relevant knowledge.

Question: {question}

Reasoning approach:
1. Identify what knowledge is needed to answer this question
2. Consider all relevant facts, constraints, and real-world conditions
3. Reason through the logical implications step by step
4. Determine if the conditions are satisfied or not

Answer: #### [yes/no]"""

        elif dataset_type == 'gpqa':
            return f"""Solve this graduate-level science question using domain expertise.

Question: {question}

Scientific reasoning approach:
1. Identify the scientific domain and relevant principles
2. Analyze each choice using appropriate scientific knowledge
3. Apply relevant formulas, theories, or experimental facts
4. Eliminate incorrect options systematically
5. Select the best answer based on scientific reasoning

Answer: #### [A/B/C/D]"""

        elif dataset_type == 'mmlu':
            return f"""Answer this academic question using relevant domain knowledge.

Question: {question}

Academic reasoning approach:
1. Identify the specific academic discipline and key concepts
2. Apply relevant theories, facts, or methodologies from that field
3. Evaluate each option against domain-specific criteria
4. Use systematic elimination to narrow down choices
5. Select the most academically sound answer

Answer: #### [A/B/C/D]"""

        else:
            # Fallback to original generic prompt
            return f"""Please answer the following question directly and concisely:

Question: {question}

Answer in a single sentence. For math problems, end with #### [number].
For yes/no questions, start with Yes or No.
For multiple choice, provide only the letter choice.

Answer:"""
    
    async def generate_answer(self, problem: Dict[str, Any]) -> str:
        """Generate direct answer without reasoning steps using specialized prompts"""
        
        # Detect dataset type first
        dataset_type = self._detect_dataset_type(problem)
        
        # Extract question from different possible keys
        # Handle different dataset formats
        if 'context' in problem and isinstance(problem['context'], list):
            # HotpotQA format - build context + question
            context = problem['context']
            context_text = ""
            for ctx in context:
                if isinstance(ctx, list) and len(ctx) >= 2:
                    title = ctx[0]
                    paragraphs = ctx[1]
                    if isinstance(paragraphs, list):
                        context_text += f"\n{title}:\n" + " ".join(paragraphs) + "\n"
                    else:
                        context_text += f"\n{title}:\n{paragraphs}\n"
            question = f"Context: {context_text.strip()}\n\nQuestion: {problem.get('question', '')}"
        elif 'context' in problem:
            # DROP dataset format: context contains passage + question
            question = problem.get('context', '')
        else:
            # Standard format: prompt, question, or problem fields
            question = problem.get('prompt') or problem.get('question') or problem.get('problem') or ''

        # Handle case where question might be a list (e.g., hotpotqa)
        if isinstance(question, list):
            question = ' '.join(str(x) for x in question)
        elif not isinstance(question, str):
            question = str(question)
        
        # Use specialized prompt based on detected dataset type
        prompt = self._get_specialized_prompt(dataset_type, question)
        
        # Get answer from API with slightly lower temperature for more consistency
        answer = await self._api_call(prompt, temperature=0.05)
        return answer