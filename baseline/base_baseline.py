#!/usr/bin/env python3
"""
Base class for baseline methods
"""

import asyncio
import json
import os
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
from utils.api import async_generate_completion
from utils.answer_extraction import create_answer_entry


class BaseBaseline(ABC):
    """Base class for all baseline methods"""

    def __init__(self, temperature: float = 0.7, model: str = "openai"):
        self.model = model  # Model to use for experiments
        self.temperature = temperature
        self.results = []
    
    @abstractmethod
    def get_method_name(self) -> str:
        """Return the name of the baseline method"""
        pass
    
    @abstractmethod
    async def generate_answer(self, problem: Dict[str, Any]) -> str:
        """Generate answer for a single problem"""
        pass
    
    async def process_dataset(self, jsonl_file: str, max_problems: int = 50, output_file: str = None) -> Dict[str, Any]:
        """Process entire dataset and return results with resume capability"""
        problems = self._load_problems(jsonl_file, max_problems)
        
        # Check for existing results and resume from where we left off
        existing_results, start_index = self._load_existing_results(output_file, problems)
        
        if start_index > 0:
            print(f"📂 Found existing results: {start_index} problems already completed")
            print(f"🔄 Resuming from problem {start_index + 1}/{len(problems)}")
        else:
            print(f"🚀 Starting fresh: Processing {len(problems)} problems with {self.get_method_name()} method using {self.model} model")
        
        results = existing_results.copy()
        
        for i in range(start_index, len(problems)):
            problem = problems[i]
            # Handle different ID field names for different datasets
            problem_id = problem.get('id') or problem.get('task_id') or str(i + 1)
            print(f"Processing problem {i + 1}/{len(problems)}: {problem_id}")

            try:
                answer = await self.generate_answer(problem)

                # Extract question content from different dataset formats
                # Handle HotpotQA format with context
                if 'context' in problem and isinstance(problem['context'], list):
                    # HotpotQA format - build context + question
                    context = problem['context']
                    context_text = ""
                    for ctx in context:
                        if isinstance(ctx, list) and len(ctx) >= 2:
                            title = ctx[0]
                            paragraphs = ctx[1]
                            if isinstance(paragraphs, list):
                                context_text += f"\n{title}:\n" + " ".join(paragraphs) + "\n"
                            else:
                                context_text += f"\n{title}:\n{paragraphs}\n"
                    question_content = f"Context: {context_text.strip()}\n\nQuestion: {problem.get('question', '')}"
                else:
                    # Standard format
                    question_content = (
                        problem.get('question') or
                        problem.get('problem') or
                        problem.get('prompt') or
                        problem.get('context') or
                        ''
                    )

                # Create result entry compatible with existing evaluator
                result = {
                    "id": str(problem_id),
                    "question": question_content,
                    "answer": answer,
                    "method": self.get_method_name(),
                    "model": self.model,
                    "execution_time": 0.0  # Placeholder for execution time
                }
                
                results.append(result)
                
                # Save incrementally every 5 problems to avoid losing progress
                if output_file and (i + 1) % 5 == 0:
                    self._save_results_incremental(results, output_file)
                
            except Exception as e:
                print(f"Error processing problem {i + 1}: {e}")

                # Extract question content from different dataset formats
                question_content = (
                    problem.get('question') or
                    problem.get('problem') or
                    problem.get('prompt') or
                    problem.get('context') or
                    ''
                )

                error_result = {
                    "id": str(problem_id),
                    "question": question_content,
                    "answer": f"Error: {str(e)}",
                    "method": self.get_method_name(),
                    "model": self.model,
                    "execution_time": 0.0
                }
                results.append(error_result)
                
                # Save incrementally even for errors
                if output_file and (i + 1) % 5 == 0:
                    self._save_results_incremental(results, output_file)
        
        # Save results if output file specified
        if output_file:
            os.makedirs(os.path.dirname(output_file), exist_ok=True)
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            print(f"Results saved to {output_file}")
        
        return {
            'method': self.get_method_name(),
            'model': self.model,
            'total_problems': len(problems),
            'results': results
        }
    
    def _load_problems(self, jsonl_file: str, max_problems: int) -> List[Dict[str, Any]]:
        """Load problems from JSONL file"""
        problems = []
        with open(jsonl_file, 'r', encoding='utf-8') as f:
            for line in f:
                if line.strip():
                    problems.append(json.loads(line))
                    if len(problems) >= max_problems:
                        break
        return problems
    
    async def _api_call(self, prompt: str, temperature: float = None) -> str:
        """Make API call with error handling"""
        if temperature is None:
            temperature = self.temperature
            
        try:
            response = await async_generate_completion(
                agent_id=self.model,
                prompt=prompt,
                temperature=temperature,
                max_tokens=2048
            )
            return response.strip() if response else ""
        except Exception as e:
            print(f"API call failed: {e}")
            return f"Error: API call failed - {str(e)}"
    
    def extract_final_answer(self, solution: str) -> str:
        """Unified answer extraction for all baselines"""
        solution = solution.strip()
        
        import re
        
        # Priority 1: Look for FINAL ANSWER: pattern
        final_answer_pattern = r"FINAL ANSWER:\s*(.+)"
        match = re.search(final_answer_pattern, solution, re.IGNORECASE | re.MULTILINE)
        if match:
            return match.group(1).strip()
        
        # Priority 2: Look for #### pattern (math problems)
        math_pattern = r"####\s*([0-9.,\-+*/\s\w]+)"
        match = re.search(math_pattern, solution, re.MULTILINE)
        if match:
            return match.group(1).strip()
        
        # Priority 3: Look for common answer patterns
        answer_patterns = [
            r"(?:final answer|answer|therefore|thus|so)\s*:?\s*([^.\n]+)",
            r"(?:the answer is|answer is)\s*:?\s*([^.\n]+)",
            r"\$\$([^$]+)\$\$",  # LaTeX math
            r"```\s*([^`]+)\s*```",  # Code blocks
        ]
        
        for pattern in answer_patterns:
            matches = re.findall(pattern, solution, re.IGNORECASE)
            if matches:
                return matches[-1].strip()  # Return last match
        
        # If no pattern matches, return last non-empty line
        lines = [line.strip() for line in solution.split('\n') if line.strip()]
        if lines:
            return lines[-1]
        
        return solution[:100]  # Fallback to first 100 chars

# Removed _save_answers_only method as it was generating unused files

    def _load_existing_results(self, output_file: str, problems: List[Dict[str, Any]]) -> tuple[List[Dict[str, Any]], int]:
        """Load existing results and determine where to resume from"""
        if not output_file or not os.path.exists(output_file):
            return [], 0
        
        try:
            with open(output_file, 'r', encoding='utf-8') as f:
                existing_results = json.load(f)
            
            # Validate existing results format
            if not isinstance(existing_results, list):
                print(f"⚠️  Invalid results format in {output_file}, starting fresh")
                return [], 0
            
            # Check consistency with current problems
            if len(existing_results) > len(problems):
                print(f"⚠️  Existing results have more problems than requested, truncating")
                existing_results = existing_results[:len(problems)]
            
            # Verify problem IDs match (if available)
            start_index = len(existing_results)
            for i, result in enumerate(existing_results):
                if i < len(problems):
                    current_problem_id = str(problems[i].get('id') or problems[i].get('task_id') or str(i + 1))
                    existing_problem_id = str(result.get('id', ''))
                    
                    if current_problem_id != existing_problem_id:
                        print(f"⚠️  Problem ID mismatch at position {i}, starting fresh")
                        return [], 0
            
            return existing_results, start_index
            
        except Exception as e:
            print(f"⚠️  Error loading existing results from {output_file}: {e}")
            return [], 0

    def _save_results_incremental(self, results: List[Dict[str, Any]], output_file: str):
        """Save results incrementally to avoid losing progress"""
        try:
            os.makedirs(os.path.dirname(output_file), exist_ok=True)
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            print(f"📁 Progress saved: {len(results)} problems completed")
            
        except Exception as e:
            print(f"⚠️  Error saving incremental results: {e}")