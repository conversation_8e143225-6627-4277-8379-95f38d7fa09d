import os
import hashlib
import json
import random
import time
import logging
import asyncio
import aiohttp
import concurrent.futures
import threading
from collections import OrderedDict
from typing import Dict, List, Any, Optional, Callable, Union
from datetime import datetime

import openai
from openai import AsyncOpenAI
import anthropic

try:
    from google import genai
except ImportError:
    genai = None

from config import get_config, MODEL_CONFIG, DEBUG_MODE, ENABLE_CACHE, get_format_instructions

class LimitedSizeCache(OrderedDict):
    def __init__(self, max_size=1000, ttl=3600, *args, **kwargs):
        self.max_size = max_size
        self.ttl = ttl
        super().__init__(*args, **kwargs)

    def __setitem__(self, key, value):
        timestamp = time.time()
        value_with_timestamp = {"value": value, "timestamp": timestamp}

        if key in self:
            self.move_to_end(key)
        super().__setitem__(key, value_with_timestamp)
        if len(self) > self.max_size:
            oldest = next(iter(self))
            del self[oldest]

    def __getitem__(self, key):
        value_with_timestamp = super().__getitem__(key)
        timestamp = value_with_timestamp["timestamp"]
        current_time = time.time()

        if current_time - timestamp > self.ttl:
            del self[key]
            raise KeyError(key)

        return value_with_timestamp["value"]

    def get(self, key, default=None):
        try:
            return self[key]
        except KeyError:
            return default

    def set(self, key, value):
        self[key] = value

    def cleanup(self):
        current_time = time.time()
        to_delete = [k for k, v in self.items() if current_time - v["timestamp"] > self.ttl]

        for key in to_delete:
            del self[key]

        while len(self) > self.max_size:
            oldest = next(iter(self))
            del self[oldest]

_shared_cache = LimitedSizeCache(
    max_size=get_config("max_cache_size", 4000),
    ttl=get_config("cache_ttl", 3600 * 24)
)
_async_semaphore = None
MAX_CONCURRENT_REQUESTS = get_config("concurrent_requests_limit", 32)

def log(message: str, prefix="API") -> None:
    if DEBUG_MODE:
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        print(f"[{prefix} {timestamp}] {message}")

def generate_cache_key(agent_id: str, prompt: str, system_prompt: str = "",
                      temperature: float = 0.7, max_tokens: int = 1000,
                      metadata: Dict[str, Any] = None) -> str:
    metadata = metadata or {}
    source_id = metadata.get('source_agent_id')
    task_type = metadata.get('task_type')

    timestamp = int(time.time() / (3600 * 24))

    if len(prompt) > 2000:
        prompt_hash = hashlib.md5((prompt[:1000] + prompt[-1000:]).encode()).hexdigest()
    else:
        prompt_hash = hashlib.md5(prompt.encode()).hexdigest()

    system_hash = hashlib.md5(system_prompt.encode()).hexdigest() if system_prompt else ""

    components = [
        f"agent_id={agent_id}",
        f"source_id={source_id}" if source_id else "",
        f"task_type={task_type}" if task_type else "",
        f"temp={temperature}",
        f"max_tokens={max_tokens}",
        f"timestamp={timestamp}",
        f"system={system_hash}" if system_hash else "",
        f"prompt={prompt_hash}"
    ]

    key = ":".join([c for c in components if c])
    return key

class RateLimiter:
    def __init__(self, max_calls_per_minute=60):
        self.max_calls = max_calls_per_minute
        self.calls = []
        self.lock = threading.Lock()

    def _clean_old_calls(self):
        now = time.time()
        self.calls = [call for call in self.calls if now - call < 60]

    def request_permission(self) -> bool:
        with self.lock:
            self._clean_old_calls()
            if len(self.calls) < self.max_calls:
                self.calls.append(time.time())
                return True
            return False

    def wait_for_permission(self, timeout=None):
        start_time = time.time()

        while True:
            with self.lock:
                self._clean_old_calls()
                if len(self.calls) < self.max_calls:
                    self.calls.append(time.time())
                    return True

            if timeout is not None and time.time() - start_time > timeout:
                return False

            time.sleep(0.1)

_api_rate_limiters = {}
_rate_limiter_lock = threading.Lock()

def get_rate_limiter(agent_id: str) -> RateLimiter:
    with _rate_limiter_lock:
        if agent_id not in _api_rate_limiters:
            limit_map = {
                "openai": 100,
                "anthropic": 50
            }
            limit = limit_map.get(agent_id, 30)
            _api_rate_limiters[agent_id] = RateLimiter(limit)

        return _api_rate_limiters[agent_id]

class ModelAdapter:
    def __init__(self, agent_id: str):
        self.agent_id = agent_id
        self.config = MODEL_CONFIG.get(agent_id, {})
        self.model = self.config.get("model", "unknown")
        self.client = None
        self.max_tokens = self.config.get("max_tokens", get_config("max_tokens", 4000))
        self.timeout = self.config.get("timeout", get_config("timeout", 30))
        self._initialize_client()

    def _initialize_client(self):
        raise NotImplementedError("Subclasses must implement _initialize_client method")

    def _get_format_instructions(self) -> str:
        return get_format_instructions(self.agent_id)

    def _format_messages(self, prompt: str, system_prompt: str = None) -> List[Dict[str, str]]:
        messages = []
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": prompt})
        return messages

    def generate_completion(self, prompt: str, system_prompt: str = None,
                           temperature: float = 0.7, max_tokens: int = None) -> str:
        raise NotImplementedError("Subclasses must implement generate_completion method")

    async def generate_completion_async(self, prompt: str, system_prompt: str = None,
                                     temperature: float = 0.7, max_tokens: int = None) -> str:
        raise NotImplementedError("Subclasses must implement generate_completion_async method")

class OpenAIAdapter(ModelAdapter):
    def _initialize_client(self):
        api_key = self.config.get("api_key", os.getenv("OPENAI_API_KEY", ""))
        if not api_key:
            raise ValueError("OpenAI API key not set")

        base_url = self.config.get("api_base", "https://api.openai.com/v1")

        self.client = openai.OpenAI(api_key=api_key, base_url=base_url)
        self.async_client = AsyncOpenAI(
            api_key=api_key,
            base_url=base_url,
            timeout=get_config("async_client_timeout", 30),  # Reduced from 60 to 30
            max_retries=0
        )

    def generate_completion(self, prompt: str, system_prompt: str = None,
                           temperature: float = 0.7, max_tokens: int = None) -> str:
        if max_tokens is None:
            max_tokens = self.max_tokens

        format_instructions = self._get_format_instructions()
        if format_instructions and system_prompt:
            system_prompt = f"{system_prompt}\n{format_instructions}"
        elif format_instructions:
            system_prompt = format_instructions

        messages = self._format_messages(prompt, system_prompt)

        response = self.client.chat.completions.create(
            model=self.model,
            messages=messages,
            temperature=temperature,
            max_tokens=max_tokens
        )

        return response.choices[0].message.content

    async def generate_completion_async(self, prompt: str, system_prompt: str = None,
                                     temperature: float = 0.7, max_tokens: int = None) -> str:
        if max_tokens is None:
            max_tokens = self.max_tokens

        format_instructions = self._get_format_instructions()
        if format_instructions and system_prompt:
            system_prompt = f"{system_prompt}\n{format_instructions}"
        elif format_instructions:
            system_prompt = format_instructions

        messages = self._format_messages(prompt, system_prompt)

        try:
            timeout = get_config("async_timeout", 30)  # Reduced from 120 to 30

            start_time = time.time()
            log(f"Starting OpenAI API request ({self.agent_id}/{self.model})", "Async")

            completion_params = {
                "model": self.model,
                "messages": messages,
                "temperature": temperature,
                "max_tokens": max_tokens,
                "timeout": timeout
            }

            response = await self.async_client.chat.completions.create(**completion_params)

            elapsed = time.time() - start_time
            log(f"OpenAI API request completed ({elapsed:.2f} seconds)", "Async")

            return response.choices[0].message.content
        except Exception as e:
            error_msg = f"OpenAI API async call error: {type(e).__name__}: {str(e)}"
            log(error_msg, "Async")

            if "timeout" in str(e).lower() or "connection" in str(e).lower():
                try:
                    fallback_max_tokens = min(max_tokens if max_tokens else self.max_tokens, 1024)
                    log(f"Trying fallback strategy with smaller max_tokens ({fallback_max_tokens})", "Async")

                    fallback_timeout = get_config("fallback_timeout", 40)

                    fallback_params = {
                        "model": self.model,
                        "messages": messages,
                        "temperature": temperature,
                        "max_tokens": fallback_max_tokens,
                        "timeout": fallback_timeout
                    }

                    start_time = time.time()
                    log(f"Starting fallback request", "Async")

                    fallback_response = await self.async_client.chat.completions.create(**fallback_params)

                    elapsed = time.time() - start_time
                    log(f"Fallback request completed ({elapsed:.2f} seconds)", "Async")

                    return fallback_response.choices[0].message.content
                except Exception as fallback_error:
                    log(f"Fallback strategy also failed: {str(fallback_error)}", "Async")

            raise

class OpenAICompatibleAdapter(ModelAdapter):
    def _initialize_client(self):
        api_key = self.config.get("api_key", "")
        if not api_key:
            env_key = self._get_env_key_name()
            api_key = os.getenv(env_key, "")
            if not api_key:
                raise ValueError(f"{self.agent_id.capitalize()} API key not set")

        if self.agent_id == "anthropic":
            # Force use of official Anthropic API for this project
            # Override any ANTHROPIC_BASE_URL environment variable
            self.client = anthropic.Anthropic(
                api_key=api_key,
                base_url="https://api.anthropic.com"
            )
            self.async_client = anthropic.AsyncAnthropic(
                api_key=api_key,
                base_url="https://api.anthropic.com"
            )
        else:
            base_url = self.config.get("api_base", "")
            self.client = openai.OpenAI(api_key=api_key, base_url=base_url)
            self.async_client = AsyncOpenAI(api_key=api_key, base_url=base_url)

    def _get_env_key_name(self) -> str:
        # Use official API key naming conventions
        env_key_map = {
            "anthropic": "ANTHROPIC_API_KEY",
            "deepseek": "DEEPSEEK_API_KEY",
            "qwen": "DASHSCOPE_API_KEY",
            "llama": "GROQ_API_KEY",
            "gemini": "GEMINI_API_KEY",
            "openai": "OPENAI_API_KEY"
        }
        return env_key_map.get(self.agent_id, f"{self.agent_id.upper()}_API_KEY")

    def generate_completion(self, prompt: str, system_prompt: str = None,
                           temperature: float = 0.7, max_tokens: int = None) -> str:
        if max_tokens is None:
            max_tokens = self.max_tokens

        format_instructions = self._get_format_instructions()
        if format_instructions and system_prompt:
            system_prompt = f"{system_prompt}\n{format_instructions}"
        elif format_instructions:
            system_prompt = format_instructions

        if self.agent_id == "anthropic":
            # Use agents-style API call
            response = self.client.messages.create(
                model=self.model,
                max_tokens=max_tokens,
                temperature=temperature,
                system=system_prompt or "",
                messages=[
                    {"role": "user", "content": prompt}
                ]
            )

            # Extract text from response blocks like agents does
            for block in response.content:
                if block.type == "text":
                    return block.text

            # Fallback if no text block found
            return "No text response received"
        else:
            messages = self._format_messages(prompt, system_prompt)

            try:
                response = self.client.chat.completions.create(
                    model=self.model,
                    messages=messages,
                    temperature=temperature,
                    max_tokens=max_tokens
                )

                return response.choices[0].message.content
            except Exception as e:
                raise

    async def generate_completion_async(self, prompt: str, system_prompt: str = None,
                                     temperature: float = 0.7, max_tokens: int = None) -> str:
        if max_tokens is None:
            max_tokens = self.max_tokens

        format_instructions = self._get_format_instructions()
        if format_instructions and system_prompt:
            system_prompt = f"{system_prompt}\n{format_instructions}"
        elif format_instructions:
            system_prompt = format_instructions

        try:
            timeout = get_config("async_timeout", 30)  # Reduced from 60 to 30
            start_time = time.time()

            if self.agent_id == "anthropic":
                log(f"Starting Anthropic API request ({self.model})", "Async")

                try:
                    # Use agents-style async API call
                    response = await self.async_client.messages.create(
                        model=self.model,
                        max_tokens=max_tokens,
                        temperature=temperature,
                        system=system_prompt or "",
                        messages=[{"role": "user", "content": prompt}]
                    )

                    elapsed = time.time() - start_time
                    log(f"Anthropic API request completed ({elapsed:.2f} seconds)", "Async")

                    # Extract text from response blocks like agents does
                    for block in response.content:
                        if block.type == "text":
                            return block.text

                    # Fallback if no text block found
                    return "No text response received"
                except Exception as e:
                    if "timeout" in str(e).lower() or "connection" in str(e).lower():
                        fallback_max_tokens = min(max_tokens if max_tokens else self.max_tokens, 1024)
                        log(f"Trying fallback strategy with smaller max_tokens ({fallback_max_tokens})", "Async")

                        fallback_timeout = get_config("fallback_timeout", 40)

                        fallback_response = await self.async_client.messages.create(
                            model=self.model,
                            max_tokens=fallback_max_tokens,
                            temperature=temperature,
                            system=system_prompt or "",
                            messages=[{"role": "user", "content": prompt}]
                        )

                        # Extract text from fallback response blocks like agents does
                        for block in fallback_response.content:
                            if block.type == "text":
                                return block.text

                        return "No text response received from fallback"
                    raise
            else:
                messages = self._format_messages(prompt, system_prompt)
                log(f"Starting {self.agent_id.capitalize()} API request ({self.model})", "Async")

                try:
                    response = await self.async_client.chat.completions.create(
                        model=self.model,
                        messages=messages,
                        temperature=temperature,
                        max_tokens=max_tokens,
                        timeout=timeout
                    )

                    elapsed = time.time() - start_time
                    log(f"{self.agent_id.capitalize()} API request completed ({elapsed:.2f} seconds)", "Async")

                    return response.choices[0].message.content
                except Exception as e:
                    if "timeout" in str(e).lower() or "connection" in str(e).lower():
                        fallback_max_tokens = min(max_tokens if max_tokens else self.max_tokens, 1024)
                        log(f"Trying fallback strategy with smaller max_tokens ({fallback_max_tokens})", "Async")

                        fallback_timeout = get_config("fallback_timeout", 40)

                        fallback_response = await self.async_client.chat.completions.create(
                            model=self.model,
                            messages=messages,
                            temperature=temperature,
                            max_tokens=fallback_max_tokens,
                            timeout=fallback_timeout
                        )

                        return fallback_response.choices[0].message.content
                    raise

        except Exception as e:
            log(f"{self.agent_id.capitalize()} API async call error: {str(e)}", "Async")
            raise

class GeminiAdapter(ModelAdapter):
    def _initialize_client(self):
        api_key = self.config.get("api_key", os.getenv("GEMINI_API_KEY", ""))
        if not api_key:
            raise ValueError("Gemini API key not set")

        try:
            from openai import OpenAI
        except ImportError:
            raise ImportError("The 'openai' package is required for Gemini API. Install it with 'pip install openai>=1.0.0'")

        self.client = OpenAI(
            api_key=api_key,
            base_url="https://generativelanguage.googleapis.com/v1beta/openai/"
        )

    def generate_completion(self, prompt: str, system_prompt: str = None,
                          temperature: float = 0.7, max_tokens: int = None) -> str:
        if max_tokens is None:
            max_tokens = self.max_tokens

        format_instructions = self._get_format_instructions()
        if format_instructions and system_prompt:
            system_prompt = f"{system_prompt}\n{format_instructions}"
        elif format_instructions:
            system_prompt = format_instructions

        messages = []
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})

        messages.append({"role": "user", "content": prompt})

        try:
            start_time = time.time()
            log(f"Starting Gemini API request via OpenAI SDK ({self.agent_id}/{self.model})")

            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens
            )

            log(f"Gemini API request completed in {time.time() - start_time:.2f}s")

            if not response.choices or len(response.choices) == 0:
                return "No response generated"

            return response.choices[0].message.content

        except Exception as e:
            log(f"Error in Gemini API request: {str(e)}")
            raise

    async def generate_completion_async(self, prompt: str, system_prompt: str = None,
                                      temperature: float = 0.7, max_tokens: int = None) -> str:
        # 使用执行器运行同步方法，因为我们没有使用异步客户端
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            None,
            lambda: self.generate_completion(
                prompt=prompt,
                system_prompt=system_prompt,
                temperature=temperature,
                max_tokens=max_tokens
            )
        )

class ModelAdapterFactory:
    _adapter_instances = {}
    _adapter_lock = threading.Lock()

    @classmethod
    def get_adapter(cls, agent_id: str) -> ModelAdapter:
        with cls._adapter_lock:
            if agent_id in cls._adapter_instances:
                return cls._adapter_instances[agent_id]

            adapter_map = {
                "openai": OpenAIAdapter,
                "anthropic": OpenAICompatibleAdapter,
                "deepseek": OpenAICompatibleAdapter,
                "qwen": OpenAICompatibleAdapter,
                "llama": OpenAICompatibleAdapter,
                "gemini": GeminiAdapter
            }

            adapter_class = adapter_map.get(agent_id)
            if not adapter_class:
                raise ValueError(f"No adapter available for agent '{agent_id}'")

            adapter = adapter_class(agent_id)
            cls._adapter_instances[agent_id] = adapter
            return adapter

class APICallHandler:
    def __init__(self):
        self.max_retries = get_config("retry_attempts", 3)
        self.base_delay = get_config("retry_base_delay", 1)
        self.timeout = get_config("timeout", 30)
        self.cache_enabled = get_config("cache", ENABLE_CACHE)
        self.executor = concurrent.futures.ThreadPoolExecutor(
            max_workers=get_config("max_workers", 16)
        )

    def __del__(self):
        """Cleanup when object is destroyed."""
        try:
            if hasattr(self, 'executor') and self.executor:
                self.executor.shutdown(wait=False)
        except:
            pass

    def call_with_retry(self, api_call_func: Callable, *args, **kwargs) -> Any:
        retries = 0
        last_exception = None

        while retries <= self.max_retries:
            try:
                return api_call_func(*args, **kwargs)
            except Exception as e:
                last_exception = e
                retries += 1

                if retries <= self.max_retries:
                    wait_time = self.base_delay * (2 ** (retries - 1)) * (0.5 + random.random())
                    log(f"Retrying API call in {wait_time:.2f} seconds (attempt {retries}/{self.max_retries})")
                    time.sleep(wait_time)

        if last_exception:
            raise last_exception
        else:
            raise Exception(f"API call failed after {self.max_retries} retries")

    def async_call(self, api_call_func: Callable, *args, **kwargs) -> concurrent.futures.Future:
        return self.executor.submit(self.call_with_retry, api_call_func, *args, **kwargs)

async def _ensure_semaphore():
    global _async_semaphore
    if _async_semaphore is None:
        _async_semaphore = asyncio.Semaphore(MAX_CONCURRENT_REQUESTS)
    return _async_semaphore

class APIService:
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(APIService, cls).__new__(cls)
            cls._instance.api_handler = APICallHandler()
        return cls._instance

    def get_adapter(self, agent_id: str) -> ModelAdapter:
        return ModelAdapterFactory.get_adapter(agent_id)

    def generate_completion(self, agent_id: str, prompt: str, system_prompt: str = None,
                          temperature: float = 0.7, max_tokens: int = None,
                          skip_cache: bool = False, metadata: Dict[str, Any] = None) -> str:

        def _api_call():
            adapter = self.get_adapter(agent_id)
            return adapter.generate_completion(
                prompt=prompt,
                system_prompt=system_prompt,
                temperature=temperature,
                max_tokens=max_tokens
            )

        global _shared_cache
        cache_key = None
        if self.api_handler.cache_enabled and not skip_cache:
            # Simplified caching - no special COT handling
                cache_key = generate_cache_key(agent_id, prompt, system_prompt, temperature, max_tokens or 0, metadata)
                cached_result = _shared_cache.get(cache_key)
                if cached_result:
                    log(f"Cache hit for {agent_id} ({metadata})")
                    return cached_result

        result = self.api_handler.call_with_retry(_api_call)

        if self.api_handler.cache_enabled and cache_key and not skip_cache:
            _shared_cache.set(cache_key, result)

        return result

    def generate_completion_async(self, agent_id: str, prompt: str, system_prompt: str = None,
                                temperature: float = 0.7, max_tokens: int = None,
                                skip_cache: bool = False, metadata: Dict[str, Any] = None) -> concurrent.futures.Future:

        def _api_call():
            adapter = self.get_adapter(agent_id)
            return adapter.generate_completion(
                prompt=prompt,
                system_prompt=system_prompt,
                temperature=temperature,
                max_tokens=max_tokens
            )

        global _shared_cache
        cache_key = None
        if self.api_handler.cache_enabled and not skip_cache:
            # Simplified caching - no special COT handling
                cache_key = generate_cache_key(agent_id, prompt, system_prompt, temperature, max_tokens or 0, metadata)
                cached_result = _shared_cache.get(cache_key)
                if cached_result:
                    log(f"Cache hit for {agent_id} (async) ({metadata})")
                    future = concurrent.futures.Future()
                    future.set_result(cached_result)
                    return future

        future = self.api_handler.async_call(_api_call)

        if self.api_handler.cache_enabled and cache_key and not skip_cache:
            def on_done(f):
                try:
                    result = f.result()
                    _shared_cache.set(cache_key, result)
                except:
                    pass

            future.add_done_callback(on_done)

        return future

    def clear_cache(self):
        global _shared_cache
        _shared_cache = LimitedSizeCache(
            max_size=get_config("max_cache_size", 4000),
            ttl=get_config("cache_ttl", 3600 * 24)
        )

    def enable_cache(self, enabled: bool = True):
        self.api_handler.cache_enabled = enabled

def generate_completion(
    agent_id: str,
    prompt: str,
    system_prompt: Optional[str] = None,
    temperature: float = 0.7,
    max_tokens: int = None,
    top_p: float = 1.0,
    n: int = 1,
    retry_count: int = 0,
    skip_cache: bool = False,
    metadata: Dict[str, Any] = None
) -> Optional[str]:
    try:
        api_service = APIService()
        return api_service.generate_completion(
            agent_id=agent_id,
            prompt=prompt,
            system_prompt=system_prompt,
            temperature=temperature,
            max_tokens=max_tokens,
            skip_cache=skip_cache,
            metadata=metadata
        )
    except Exception as e:
        log(f"Error generating completion with {agent_id}: {str(e)}")
        if "content_filter" in str(e).lower():
            return "The model's content filter was triggered. Please modify your prompt and try again."
        if "context_length" in str(e).lower() or "tokens" in str(e).lower():
            return "The input was too long for the model. Please reduce the input length and try again."
        return None

def generate_completion_async(
    agent_id: str,
    prompt: str,
    system_prompt: Optional[str] = None,
    temperature: float = 0.7,
    max_tokens: int = None,
    skip_cache: bool = False,
    metadata: Dict[str, Any] = None
) -> concurrent.futures.Future:
    api_service = APIService()
    return api_service.generate_completion_async(
        agent_id=agent_id,
        prompt=prompt,
        system_prompt=system_prompt,
        temperature=temperature,
        max_tokens=max_tokens,
        skip_cache=skip_cache,
        metadata=metadata
    )

async def async_generate_completion(
    agent_id: str,
    prompt: str,
    system_prompt: Optional[str] = None,
    temperature: float = 0.7,
    max_tokens: int = None,
    skip_cache: bool = False,
    metadata: Dict[str, Any] = None,
    retry_count: int = 3,
    retry_delay: float = 1.0
) -> str:
    global _shared_cache
    cache_key = None
    cache_enabled = ENABLE_CACHE and not skip_cache

    if cache_enabled:
        cache_key = generate_cache_key(agent_id, prompt, system_prompt, temperature, max_tokens or 0, metadata)
        cached_result = _shared_cache.get(cache_key)
        if cached_result:
            log(f"Cache hit for {agent_id} ({metadata})", "Async")
            return cached_result

    semaphore = await _ensure_semaphore()

    attempts = 0
    last_error = None

    while attempts <= retry_count:
        try:
            async with semaphore:
                adapter = ModelAdapterFactory.get_adapter(agent_id)
                result = await adapter.generate_completion_async(
                    prompt=prompt,
                    system_prompt=system_prompt,
                    temperature=temperature,
                    max_tokens=max_tokens
                )

                if cache_enabled and cache_key:
                    _shared_cache.set(cache_key, result)

                return result
        except Exception as e:
            attempts += 1
            last_error = e

            if attempts <= retry_count:
                current_retry_delay = retry_delay * (2 ** (attempts - 1)) * (0.5 + random.random())
                log(f"Retrying {agent_id} API call in {current_retry_delay:.2f} seconds (attempt {attempts}/{retry_count})", "Async")
                await asyncio.sleep(current_retry_delay)
            else:
                log(f"All retry attempts failed for {agent_id}", "Async")
                break

    raise last_error or Exception(f"Failed to generate completion after {retry_count} retries")

async def async_batch_generate(
    requests: List[Dict[str, Any]],
    batch_size: int = None
) -> List[Dict[str, Any]]:
    from config import get_config

    if batch_size is None:
        batch_size = get_config("async_batch_size", 5)

    results = []

    for i in range(0, len(requests), batch_size):
        batch = requests[i:i+batch_size]
        tasks = []

        for req in batch:
            task = asyncio.create_task(async_generate_completion(
                agent_id=req["agent_id"],
                prompt=req["prompt"],
                system_prompt=req.get("system_prompt"),
                temperature=req.get("temperature", 0.7),
                max_tokens=req.get("max_tokens"),
                skip_cache=req.get("skip_cache", False),
                metadata=req.get("metadata", {})
            ))
            tasks.append((req, task))

        for req, task in tasks:
            try:
                result = await task
                results.append({
                    "agent_id": req["agent_id"],
                    "prompt": req["prompt"],
                    "result": result,
                    "success": True
                })
            except Exception as e:
                log(f"Error in batch processing: {str(e)}", "Async")
                results.append({
                    "agent_id": req["agent_id"],
                    "prompt": req["prompt"],
                    "error": str(e),
                    "success": False
                })

    return results

async def update_cache_size(new_size: int) -> bool:
    global _shared_cache
    if new_size <= 0:
        return False

    try:
        old_size = _shared_cache.max_size

        if old_size == new_size:
            return True

        new_cache = LimitedSizeCache(max_size=new_size, ttl=_shared_cache.ttl)

        for key, value in _shared_cache.items():
            new_cache[key] = value

        _shared_cache = new_cache
        log(f"Updated cache size: {old_size} -> {new_size}", "Async")
        return True
    except Exception as e:
        log(f"Error updating cache size: {str(e)}", "Async")
        return False

async def update_concurrency_limit(new_limit: int) -> bool:
    global _async_semaphore, MAX_CONCURRENT_REQUESTS

    if new_limit <= 0:
        return False

    if new_limit == MAX_CONCURRENT_REQUESTS:
        return True

    try:
        MAX_CONCURRENT_REQUESTS = new_limit
        _async_semaphore = asyncio.Semaphore(new_limit)
        log(f"Updated concurrency limit: {new_limit}", "Async")
        return True
    except Exception as e:
        log(f"Error updating concurrency limit: {str(e)}", "Async")
        return False

def clear_cache():
    api_service = APIService()
    api_service.clear_cache()
    log("Cache cleared")
    return True

def enable_cache(enabled: bool = True):
    api_service = APIService()
    api_service.enable_cache(enabled)
    log(f"Cache {'enabled' if enabled else 'disabled'}")
    return True