#!/usr/bin/env python3
"""
效率指标统计和追踪系统
实现 token 使用量统计、上下文长度追踪等效率指标，生成与 baseline 对比的数据
"""

from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import json
import time
from pathlib import Path
import statistics

# 可选的可视化依赖
try:
    import matplotlib.pyplot as plt
    import seaborn as sns
    VISUALIZATION_AVAILABLE = True
except ImportError:
    VISUALIZATION_AVAILABLE = False
    print("⚠️ Visualization libraries not available. Install with: pip install matplotlib seaborn")

from utils.token_utils import Token<PERSON>ounter
from config import get_config

class OperationType(Enum):
    """操作类型"""
    WORKER_DRAFT = "worker_draft"
    WORKER_ANNOTATION = "worker_annotation"
    MERGER_FUSION = "merger_fusion"
    LEADER_EVALUATION = "leader_evaluation"
    CONTEXT_COMPRESSION = "context_compression"
    COLLABORATION_ROUND = "collaboration_round"
    FULL_PROCESS = "full_process"

@dataclass
class TokenUsage:
    """Token使用统计"""
    input_tokens: int = 0
    output_tokens: int = 0
    total_tokens: int = 0
    
    def __post_init__(self):
        if self.total_tokens == 0:
            self.total_tokens = self.input_tokens + self.output_tokens
    
    def __add__(self, other):
        return TokenUsage(
            input_tokens=self.input_tokens + other.input_tokens,
            output_tokens=self.output_tokens + other.output_tokens,
            total_tokens=self.total_tokens + other.total_tokens
        )

@dataclass
class OperationMetrics:
    """单次操作指标"""
    operation_id: str = field(default_factory=lambda: str(int(time.time() * 1000000)))
    operation_type: OperationType = OperationType.WORKER_DRAFT
    agent_id: str = ""
    start_time: float = field(default_factory=time.time)
    end_time: Optional[float] = None
    duration: Optional[float] = None
    
    # Token统计
    token_usage: TokenUsage = field(default_factory=TokenUsage)
    
    # 上下文统计
    context_length_before: int = 0
    context_length_after: int = 0
    context_compression_ratio: float = 1.0
    
    # 质量指标
    quality_score: float = 0.0
    success: bool = True
    error_message: str = ""
    
    # 协作指标
    round_number: int = 0
    total_rounds: int = 0
    
    def finish(self, success: bool = True, error_message: str = ""):
        """完成操作记录"""
        self.end_time = time.time()
        self.duration = self.end_time - self.start_time
        self.success = success
        self.error_message = error_message

@dataclass
class SessionMetrics:
    """会话级别指标"""
    session_id: str
    task_type: str
    question_id: str = ""
    start_time: float = field(default_factory=time.time)
    end_time: Optional[float] = None
    total_duration: Optional[float] = None
    
    # 操作记录
    operations: List[OperationMetrics] = field(default_factory=list)
    
    # 汇总统计
    total_token_usage: TokenUsage = field(default_factory=TokenUsage)
    total_rounds: int = 0
    final_quality_score: float = 0.0
    success: bool = True
    
    # 效率指标
    tokens_per_second: float = 0.0
    operations_per_round: float = 0.0
    average_context_length: float = 0.0
    compression_efficiency: float = 0.0
    
    def finish(self, final_quality_score: float = 0.0, success: bool = True):
        """完成会话记录"""
        self.end_time = time.time()
        self.total_duration = self.end_time - self.start_time
        self.final_quality_score = final_quality_score
        self.success = success
        
        # 计算汇总统计
        self._calculate_summary_stats()
    
    def _calculate_summary_stats(self):
        """计算汇总统计"""
        if not self.operations:
            return
        
        # Token使用汇总
        for op in self.operations:
            self.total_token_usage += op.token_usage
        
        # 效率指标
        if self.total_duration and self.total_duration > 0:
            self.tokens_per_second = self.total_token_usage.total_tokens / self.total_duration
        
        if self.total_rounds > 0:
            self.operations_per_round = len(self.operations) / self.total_rounds
        
        # 上下文长度统计
        context_lengths = [op.context_length_after for op in self.operations if op.context_length_after > 0]
        if context_lengths:
            self.average_context_length = statistics.mean(context_lengths)
        
        # 压缩效率
        compression_ops = [op for op in self.operations if op.operation_type == OperationType.CONTEXT_COMPRESSION]
        if compression_ops:
            compression_ratios = [op.context_compression_ratio for op in compression_ops]
            self.compression_efficiency = statistics.mean(compression_ratios)

class EfficiencyTracker:
    """效率追踪器"""
    
    def __init__(self, output_dir: str = "results/efficiency"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 当前会话
        self.current_session: Optional[SessionMetrics] = None
        self.current_operation: Optional[OperationMetrics] = None
        
        # 历史记录
        self.session_history: List[SessionMetrics] = []
        self.baseline_data: Dict[str, Any] = {}
        
        # 配置
        self.track_detailed_metrics = get_config("track_detailed_metrics", True)
        self.save_individual_sessions = get_config("save_individual_sessions", True)
    
    def start_session(self, session_id: str, task_type: str, question_id: str = "") -> SessionMetrics:
        """开始新会话"""
        
        if self.current_session:
            print(f"⚠️ Previous session {self.current_session.session_id} not properly finished")
            self.finish_session()
        
        self.current_session = SessionMetrics(
            session_id=session_id,
            task_type=task_type,
            question_id=question_id
        )
        
        print(f"📊 Started efficiency tracking for session: {session_id}")
        return self.current_session
    
    def start_operation(self, operation_type: OperationType, agent_id: str = "",
                       round_number: int = 0, context_length: int = 0) -> OperationMetrics:
        """开始新操作"""

        if not self.current_session:
            raise ValueError("No active session. Call start_session() first.")

        # 静默完成之前的操作
        if self.current_operation:
            self.finish_operation()

        self.current_operation = OperationMetrics(
            operation_type=operation_type,
            agent_id=agent_id,
            round_number=round_number,
            context_length_before=context_length
        )

        return self.current_operation
    
    def record_token_usage(self, input_tokens: int = 0, output_tokens: int = 0,
                          total_tokens: int = 0, prompt_text: str = "", response_text: str = ""):
        """记录Token使用（支持自动计算）"""

        if not self.current_operation:
            return  # 静默跳过

        # 如果提供了文本但没有提供token数，自动计算
        if prompt_text and input_tokens == 0:
            input_tokens = TokenCounter.count_tokens(prompt_text)

        if response_text and output_tokens == 0:
            output_tokens = TokenCounter.count_tokens(response_text)

        if total_tokens == 0:
            total_tokens = input_tokens + output_tokens

        self.current_operation.token_usage = TokenUsage(
            input_tokens=input_tokens,
            output_tokens=output_tokens,
            total_tokens=total_tokens
        )
    
    def record_context_change(self, context_length_after: int, compression_ratio: float = 1.0):
        """记录上下文变化"""

        if not self.current_operation:
            return  # 静默跳过
        
        self.current_operation.context_length_after = context_length_after
        self.current_operation.context_compression_ratio = compression_ratio
    
    def finish_operation(self, quality_score: float = 0.0, success: bool = True,
                        error_message: str = ""):
        """完成当前操作"""

        if not self.current_operation:
            return  # 静默跳过
        
        self.current_operation.quality_score = quality_score
        self.current_operation.finish(success, error_message)
        
        # 添加到当前会话
        if self.current_session:
            self.current_session.operations.append(self.current_operation)
        
        self.current_operation = None
    
    def finish_session(self, final_quality_score: float = 0.0, success: bool = True):
        """完成当前会话"""

        if not self.current_session:
            return  # 静默跳过
        
        # 完成未结束的操作
        if self.current_operation:
            self.finish_operation()
        
        # 完成会话
        self.current_session.finish(final_quality_score, success)
        
        # 保存会话数据
        if self.save_individual_sessions:
            self._save_session_data(self.current_session)
        
        # 添加到历史
        self.session_history.append(self.current_session)
        
        print(f"✅ Finished efficiency tracking for session: {self.current_session.session_id}")
        print(f"   Duration: {self.current_session.total_duration:.2f}s")
        print(f"   Total tokens: {self.current_session.total_token_usage.total_tokens}")
        print(f"   Operations: {len(self.current_session.operations)}")
        
        self.current_session = None
    
    def _save_session_data(self, session: SessionMetrics):
        """保存会话数据"""
        
        session_file = self.output_dir / f"session_{session.session_id}.json"
        
        # 转换为可序列化格式
        session_data = {
            'session_id': session.session_id,
            'task_type': session.task_type,
            'question_id': session.question_id,
            'start_time': session.start_time,
            'end_time': session.end_time,
            'total_duration': session.total_duration,
            'total_rounds': session.total_rounds,
            'final_quality_score': session.final_quality_score,
            'success': session.success,
            'total_token_usage': {
                'input_tokens': session.total_token_usage.input_tokens,
                'output_tokens': session.total_token_usage.output_tokens,
                'total_tokens': session.total_token_usage.total_tokens
            },
            'efficiency_metrics': {
                'tokens_per_second': session.tokens_per_second,
                'operations_per_round': session.operations_per_round,
                'average_context_length': session.average_context_length,
                'compression_efficiency': session.compression_efficiency
            },
            'operations': []
        }
        
        # 添加操作详情
        for op in session.operations:
            op_data = {
                'operation_id': op.operation_id,
                'operation_type': op.operation_type.value,
                'agent_id': op.agent_id,
                'start_time': op.start_time,
                'end_time': op.end_time,
                'duration': op.duration,
                'token_usage': {
                    'input_tokens': op.token_usage.input_tokens,
                    'output_tokens': op.token_usage.output_tokens,
                    'total_tokens': op.token_usage.total_tokens
                },
                'context_length_before': op.context_length_before,
                'context_length_after': op.context_length_after,
                'context_compression_ratio': op.context_compression_ratio,
                'quality_score': op.quality_score,
                'success': op.success,
                'error_message': op.error_message,
                'round_number': op.round_number
            }
            session_data['operations'].append(op_data)
        
        # 保存到文件
        try:
            with open(session_file, 'w', encoding='utf-8') as f:
                json.dump(session_data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"❌ Failed to save session data: {e}")
    
    def get_efficiency_summary(self, task_type: Optional[str] = None) -> Dict[str, Any]:
        """获取效率摘要"""
        
        # 筛选会话
        sessions = self.session_history
        if task_type:
            sessions = [s for s in sessions if s.task_type == task_type]
        
        if not sessions:
            return {"error": "No sessions found"}
        
        # 计算汇总统计
        total_sessions = len(sessions)
        successful_sessions = len([s for s in sessions if s.success])
        
        # Token统计
        total_tokens = sum(s.total_token_usage.total_tokens for s in sessions)
        avg_tokens_per_session = total_tokens / total_sessions if total_sessions > 0 else 0
        
        # 时间统计
        total_duration = sum(s.total_duration for s in sessions if s.total_duration)
        avg_duration_per_session = total_duration / total_sessions if total_sessions > 0 else 0
        
        # 质量统计
        quality_scores = [s.final_quality_score for s in sessions if s.final_quality_score > 0]
        avg_quality = statistics.mean(quality_scores) if quality_scores else 0
        
        # 效率指标
        tokens_per_second_values = [s.tokens_per_second for s in sessions if s.tokens_per_second > 0]
        avg_tokens_per_second = statistics.mean(tokens_per_second_values) if tokens_per_second_values else 0
        
        # 上下文效率
        context_lengths = [s.average_context_length for s in sessions if s.average_context_length > 0]
        avg_context_length = statistics.mean(context_lengths) if context_lengths else 0
        
        # 压缩效率
        compression_efficiencies = [s.compression_efficiency for s in sessions if s.compression_efficiency > 0]
        avg_compression_efficiency = statistics.mean(compression_efficiencies) if compression_efficiencies else 1.0
        
        return {
            'summary': {
                'total_sessions': total_sessions,
                'successful_sessions': successful_sessions,
                'success_rate': successful_sessions / total_sessions if total_sessions > 0 else 0,
                'task_type': task_type or 'all'
            },
            'token_efficiency': {
                'total_tokens_used': total_tokens,
                'average_tokens_per_session': avg_tokens_per_session,
                'average_tokens_per_second': avg_tokens_per_second
            },
            'time_efficiency': {
                'total_duration': total_duration,
                'average_duration_per_session': avg_duration_per_session,
                'average_operations_per_session': statistics.mean([len(s.operations) for s in sessions]) if sessions else 0
            },
            'quality_metrics': {
                'average_quality_score': avg_quality,
                'quality_score_std': statistics.stdev(quality_scores) if len(quality_scores) > 1 else 0
            },
            'context_efficiency': {
                'average_context_length': avg_context_length,
                'average_compression_efficiency': avg_compression_efficiency,
                'context_savings': (1 - avg_compression_efficiency) * 100 if avg_compression_efficiency < 1 else 0
            }
        }

    def load_baseline_data(self, baseline_file: str):
        """加载基线数据"""

        baseline_path = Path(baseline_file)
        if not baseline_path.exists():
            print(f"⚠️ Baseline file not found: {baseline_file}")
            return

        try:
            with open(baseline_path, 'r', encoding='utf-8') as f:
                self.baseline_data = json.load(f)
            print(f"✅ Loaded baseline data from {baseline_file}")
        except Exception as e:
            print(f"❌ Failed to load baseline data: {e}")

    def compare_with_baseline(self, task_type: Optional[str] = None) -> Dict[str, Any]:
        """与基线数据对比"""

        if not self.baseline_data:
            return {"error": "No baseline data loaded"}

        current_summary = self.get_efficiency_summary(task_type)
        if "error" in current_summary:
            return current_summary

        comparison_key = task_type or 'overall'
        baseline_metrics = self.baseline_data.get(comparison_key, {})

        if not baseline_metrics:
            return {"error": f"No baseline data for {comparison_key}"}

        # 计算改进比例
        def calculate_improvement(current, baseline, higher_is_better=True):
            if baseline == 0:
                return 0
            improvement = (current - baseline) / baseline * 100
            return improvement if higher_is_better else -improvement

        # Token效率对比
        token_comparison = {}
        if 'token_efficiency' in baseline_metrics:
            baseline_token = baseline_metrics['token_efficiency']
            current_token = current_summary['token_efficiency']

            token_comparison = {
                'tokens_per_session': {
                    'current': current_token['average_tokens_per_session'],
                    'baseline': baseline_token.get('average_tokens_per_session', 0),
                    'improvement_pct': calculate_improvement(
                        current_token['average_tokens_per_session'],
                        baseline_token.get('average_tokens_per_session', 1),
                        False  # 更少的token是更好的
                    )
                },
                'tokens_per_second': {
                    'current': current_token['average_tokens_per_second'],
                    'baseline': baseline_token.get('average_tokens_per_second', 0),
                    'improvement_pct': calculate_improvement(
                        current_token['average_tokens_per_second'],
                        baseline_token.get('average_tokens_per_second', 1),
                        True
                    )
                }
            }

        # 时间效率对比
        time_comparison = {}
        if 'time_efficiency' in baseline_metrics:
            baseline_time = baseline_metrics['time_efficiency']
            current_time = current_summary['time_efficiency']

            time_comparison = {
                'duration_per_session': {
                    'current': current_time['average_duration_per_session'],
                    'baseline': baseline_time.get('average_duration_per_session', 0),
                    'improvement_pct': calculate_improvement(
                        current_time['average_duration_per_session'],
                        baseline_time.get('average_duration_per_session', 1),
                        False  # 更短的时间是更好的
                    )
                }
            }

        # 质量对比
        quality_comparison = {}
        if 'quality_metrics' in baseline_metrics:
            baseline_quality = baseline_metrics['quality_metrics']
            current_quality = current_summary['quality_metrics']

            quality_comparison = {
                'quality_score': {
                    'current': current_quality['average_quality_score'],
                    'baseline': baseline_quality.get('average_quality_score', 0),
                    'improvement_pct': calculate_improvement(
                        current_quality['average_quality_score'],
                        baseline_quality.get('average_quality_score', 0.5),
                        True
                    )
                }
            }

        # 上下文效率对比
        context_comparison = {}
        if 'context_efficiency' in baseline_metrics:
            baseline_context = baseline_metrics['context_efficiency']
            current_context = current_summary['context_efficiency']

            context_comparison = {
                'context_length': {
                    'current': current_context['average_context_length'],
                    'baseline': baseline_context.get('average_context_length', 0),
                    'improvement_pct': calculate_improvement(
                        current_context['average_context_length'],
                        baseline_context.get('average_context_length', 1),
                        False  # 更短的上下文是更好的
                    )
                },
                'compression_efficiency': {
                    'current': current_context['average_compression_efficiency'],
                    'baseline': baseline_context.get('average_compression_efficiency', 1.0),
                    'improvement_pct': calculate_improvement(
                        current_context['average_compression_efficiency'],
                        baseline_context.get('average_compression_efficiency', 1.0),
                        False  # 更好的压缩比（更小的值）是更好的
                    )
                }
            }

        return {
            'comparison_type': comparison_key,
            'baseline_loaded': True,
            'token_efficiency': token_comparison,
            'time_efficiency': time_comparison,
            'quality_metrics': quality_comparison,
            'context_efficiency': context_comparison,
            'overall_improvement': self._calculate_overall_improvement(
                token_comparison, time_comparison, quality_comparison, context_comparison
            )
        }

    def _calculate_overall_improvement(self, token_comp, time_comp, quality_comp, context_comp) -> Dict[str, float]:
        """计算总体改进指标"""

        improvements = []

        # 收集所有改进百分比
        for comp_dict in [token_comp, time_comp, quality_comp, context_comp]:
            for metric_name, metric_data in comp_dict.items():
                if isinstance(metric_data, dict) and 'improvement_pct' in metric_data:
                    improvements.append(metric_data['improvement_pct'])

        if not improvements:
            return {'average_improvement': 0.0, 'positive_improvements': 0, 'total_metrics': 0}

        avg_improvement = statistics.mean(improvements)
        positive_improvements = len([imp for imp in improvements if imp > 0])

        return {
            'average_improvement': avg_improvement,
            'positive_improvements': positive_improvements,
            'total_metrics': len(improvements),
            'improvement_rate': positive_improvements / len(improvements) * 100
        }

    def generate_efficiency_report(self, task_type: Optional[str] = None,
                                 include_baseline_comparison: bool = True) -> str:
        """生成效率报告"""

        report_lines = ["# Multi-Agent Collaboration Efficiency Report", ""]

        # 基本信息
        report_lines.extend([
            f"**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"**Task Type:** {task_type or 'All Tasks'}",
            f"**Total Sessions:** {len(self.session_history)}",
            ""
        ])

        # 效率摘要
        summary = self.get_efficiency_summary(task_type)
        if "error" not in summary:
            report_lines.extend([
                "## Efficiency Summary",
                "",
                f"- **Success Rate:** {summary['summary']['success_rate']:.1%}",
                f"- **Average Tokens per Session:** {summary['token_efficiency']['average_tokens_per_session']:.0f}",
                f"- **Average Duration per Session:** {summary['time_efficiency']['average_duration_per_session']:.1f}s",
                f"- **Average Quality Score:** {summary['quality_metrics']['average_quality_score']:.3f}",
                f"- **Context Compression Savings:** {summary['context_efficiency']['context_savings']:.1f}%",
                ""
            ])

        # 基线对比
        if include_baseline_comparison and self.baseline_data:
            comparison = self.compare_with_baseline(task_type)
            if "error" not in comparison:
                report_lines.extend([
                    "## Baseline Comparison",
                    ""
                ])

                # Token效率对比
                if comparison['token_efficiency']:
                    token_comp = comparison['token_efficiency']
                    report_lines.extend([
                        "### Token Efficiency",
                        ""
                    ])

                    for metric, data in token_comp.items():
                        improvement_symbol = "📈" if data['improvement_pct'] > 0 else "📉" if data['improvement_pct'] < 0 else "➡️"
                        report_lines.append(
                            f"- **{metric.replace('_', ' ').title()}:** {data['current']:.1f} vs {data['baseline']:.1f} "
                            f"({improvement_symbol} {data['improvement_pct']:+.1f}%)"
                        )
                    report_lines.append("")

                # 时间效率对比
                if comparison['time_efficiency']:
                    time_comp = comparison['time_efficiency']
                    report_lines.extend([
                        "### Time Efficiency",
                        ""
                    ])

                    for metric, data in time_comp.items():
                        improvement_symbol = "📈" if data['improvement_pct'] > 0 else "📉" if data['improvement_pct'] < 0 else "➡️"
                        report_lines.append(
                            f"- **{metric.replace('_', ' ').title()}:** {data['current']:.1f}s vs {data['baseline']:.1f}s "
                            f"({improvement_symbol} {data['improvement_pct']:+.1f}%)"
                        )
                    report_lines.append("")

                # 总体改进
                overall = comparison['overall_improvement']
                report_lines.extend([
                    "### Overall Improvement",
                    "",
                    f"- **Average Improvement:** {overall['average_improvement']:+.1f}%",
                    f"- **Metrics Improved:** {overall['positive_improvements']}/{overall['total_metrics']} ({overall['improvement_rate']:.1f}%)",
                    ""
                ])

        # 详细统计
        if self.track_detailed_metrics:
            report_lines.extend([
                "## Detailed Statistics",
                ""
            ])

            # 按操作类型统计
            operation_stats = self._get_operation_type_stats(task_type)
            if operation_stats:
                report_lines.extend([
                    "### Operations Breakdown",
                    ""
                ])

                for op_type, stats in operation_stats.items():
                    report_lines.extend([
                        f"#### {op_type.replace('_', ' ').title()}",
                        f"- Count: {stats['count']}",
                        f"- Average Duration: {stats['avg_duration']:.2f}s",
                        f"- Average Tokens: {stats['avg_tokens']:.0f}",
                        f"- Success Rate: {stats['success_rate']:.1%}",
                        ""
                    ])

        # 建议
        report_lines.extend([
            "## Recommendations",
            ""
        ])

        recommendations = self._generate_recommendations(summary, comparison if include_baseline_comparison else None)
        for rec in recommendations:
            report_lines.append(f"- {rec}")

        return "\n".join(report_lines)

    def _get_operation_type_stats(self, task_type: Optional[str] = None) -> Dict[str, Dict[str, Any]]:
        """获取按操作类型的统计"""

        sessions = self.session_history
        if task_type:
            sessions = [s for s in sessions if s.task_type == task_type]

        operation_stats = {}

        for session in sessions:
            for op in session.operations:
                op_type = op.operation_type.value

                if op_type not in operation_stats:
                    operation_stats[op_type] = {
                        'count': 0,
                        'total_duration': 0,
                        'total_tokens': 0,
                        'successes': 0
                    }

                stats = operation_stats[op_type]
                stats['count'] += 1
                if op.duration:
                    stats['total_duration'] += op.duration
                stats['total_tokens'] += op.token_usage.total_tokens
                if op.success:
                    stats['successes'] += 1

        # 计算平均值
        for op_type, stats in operation_stats.items():
            if stats['count'] > 0:
                stats['avg_duration'] = stats['total_duration'] / stats['count']
                stats['avg_tokens'] = stats['total_tokens'] / stats['count']
                stats['success_rate'] = stats['successes'] / stats['count']

        return operation_stats

    def _generate_recommendations(self, summary: Dict[str, Any],
                                comparison: Optional[Dict[str, Any]] = None) -> List[str]:
        """生成改进建议"""

        recommendations = []

        # 基于成功率的建议
        success_rate = summary['summary']['success_rate']
        if success_rate < 0.9:
            recommendations.append(f"Success rate is {success_rate:.1%}. Consider improving error handling and robustness.")

        # 基于Token效率的建议
        avg_tokens = summary['token_efficiency']['average_tokens_per_session']
        if avg_tokens > 5000:
            recommendations.append("High token usage detected. Consider implementing more aggressive context compression.")

        # 基于时间效率的建议
        avg_duration = summary['time_efficiency']['average_duration_per_session']
        if avg_duration > 120:  # 2分钟
            recommendations.append("Long processing times detected. Consider optimizing LLM calls or implementing parallel processing.")

        # 基于质量的建议
        avg_quality = summary['quality_metrics']['average_quality_score']
        if avg_quality < 0.7:
            recommendations.append("Quality scores are below target. Consider adjusting evaluation criteria or improving collaboration mechanisms.")

        # 基于压缩效率的建议
        compression_savings = summary['context_efficiency']['context_savings']
        if compression_savings < 10:
            recommendations.append("Low context compression efficiency. Consider implementing more aggressive compression strategies.")

        # 基于基线对比的建议
        if comparison and "error" not in comparison:
            overall_improvement = comparison['overall_improvement']['average_improvement']
            if overall_improvement < 0:
                recommendations.append("Performance is below baseline. Review recent changes and consider reverting problematic modifications.")
            elif overall_improvement > 20:
                recommendations.append("Significant improvements detected. Document successful strategies for future reference.")

        if not recommendations:
            recommendations.append("System performance is within acceptable ranges. Continue monitoring for optimization opportunities.")

        return recommendations

    def save_efficiency_report(self, task_type: Optional[str] = None,
                             include_baseline_comparison: bool = True,
                             generate_charts: bool = True):
        """保存效率报告（论文插图一步到位）"""

        report_content = self.generate_efficiency_report(task_type, include_baseline_comparison)

        # 生成文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        task_suffix = f"_{task_type}" if task_type else "_all"
        report_file = self.output_dir / f"efficiency_report{task_suffix}_{timestamp}.md"

        try:
            # 生成可视化图表
            charts = {}
            if generate_charts and VISUALIZATION_AVAILABLE:
                print("📊 Generating visualization charts...")
                charts = self.generate_visualization_charts(task_type)
            elif generate_charts and not VISUALIZATION_AVAILABLE:
                print("⚠️ Visualization libraries not available. Skipping chart generation.")

            # 在报告中添加图表引用
            if charts:
                chart_section = [
                    "",
                    "## 📊 Visualization Charts",
                    "",
                    "The following charts have been generated for detailed analysis:",
                    ""
                ]

                for chart_type, chart_path in charts.items():
                    chart_name = chart_type.replace('_', ' ').title()
                    chart_section.append(f"- **{chart_name}**: `{chart_path}`")

                chart_section.extend([
                    "",
                    "These charts can be directly used in research papers and presentations.",
                    ""
                ])

                report_content += "\n".join(chart_section)

            # 保存报告
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report_content)

            print(f"✅ Efficiency report saved to {report_file}")
            if charts:
                print(f"📊 Generated {len(charts)} visualization charts")
                for chart_type, chart_path in charts.items():
                    print(f"   - {chart_type}: {chart_path}")

            return str(report_file)

        except Exception as e:
            print(f"❌ Failed to save efficiency report: {e}")
            return None

    def generate_visualization_charts(self, task_type: Optional[str] = None) -> Dict[str, str]:
        """生成可视化图表，论文插图一步到位"""

        if not VISUALIZATION_AVAILABLE:
            print("⚠️ Visualization libraries not available. Skipping chart generation.")
            print("   Install with: pip install matplotlib seaborn")
            return {}

        if not self.session_history:
            print("⚠️ No session data available for visualization")
            return {}

        # 筛选会话数据
        sessions = self.session_history
        if task_type:
            sessions = [s for s in sessions if s.task_type == task_type]

        if not sessions:
            print(f"⚠️ No sessions found for task type: {task_type}")
            return {}

        # 设置图表样式
        plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")

        saved_charts = {}
        chart_dir = self.output_dir / "charts"
        chart_dir.mkdir(exist_ok=True)

        # 1. Token使用量 vs 轮数折线图
        try:
            rounds_data = []
            tokens_data = []

            for session in sessions:
                for op in session.operations:
                    if op.operation_type == OperationType.COLLABORATION_ROUND:
                        rounds_data.append(op.round_number)
                        tokens_data.append(op.token_usage.total_tokens)

            if rounds_data and tokens_data:
                plt.figure(figsize=(10, 6))

                # 按轮数分组计算平均token使用量
                round_token_avg = {}
                for round_num, tokens in zip(rounds_data, tokens_data):
                    if round_num not in round_token_avg:
                        round_token_avg[round_num] = []
                    round_token_avg[round_num].append(tokens)

                rounds = sorted(round_token_avg.keys())
                avg_tokens = [statistics.mean(round_token_avg[r]) for r in rounds]
                std_tokens = [statistics.stdev(round_token_avg[r]) if len(round_token_avg[r]) > 1 else 0 for r in rounds]

                plt.plot(rounds, avg_tokens, marker='o', linewidth=2, markersize=8, label='Average Token Usage')
                plt.fill_between(rounds,
                               [avg - std for avg, std in zip(avg_tokens, std_tokens)],
                               [avg + std for avg, std in zip(avg_tokens, std_tokens)],
                               alpha=0.3)

                plt.xlabel('Collaboration Round', fontsize=12)
                plt.ylabel('Token Usage', fontsize=12)
                plt.title(f'Token Usage vs Collaboration Rounds{f" ({task_type})" if task_type else ""}', fontsize=14)
                plt.legend()
                plt.grid(True, alpha=0.3)

                chart_file = chart_dir / f"token_vs_round{'_' + task_type if task_type else ''}.png"
                plt.savefig(chart_file, dpi=300, bbox_inches='tight')
                plt.close()

                saved_charts['token_vs_round'] = str(chart_file)
                print(f"✅ Token vs Round chart saved: {chart_file}")

        except Exception as e:
            print(f"⚠️ Failed to generate token vs round chart: {e}")

        # 2. 质量分数分布直方图
        try:
            quality_scores = [s.final_quality_score for s in sessions if s.final_quality_score > 0]

            if quality_scores:
                plt.figure(figsize=(10, 6))

                plt.hist(quality_scores, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
                plt.axvline(statistics.mean(quality_scores), color='red', linestyle='--',
                           label=f'Mean: {statistics.mean(quality_scores):.3f}')

                plt.xlabel('Quality Score', fontsize=12)
                plt.ylabel('Frequency', fontsize=12)
                plt.title(f'Quality Score Distribution{f" ({task_type})" if task_type else ""}', fontsize=14)
                plt.legend()
                plt.grid(True, alpha=0.3)

                chart_file = chart_dir / f"quality_distribution{'_' + task_type if task_type else ''}.png"
                plt.savefig(chart_file, dpi=300, bbox_inches='tight')
                plt.close()

                saved_charts['quality_distribution'] = str(chart_file)
                print(f"✅ Quality distribution chart saved: {chart_file}")

        except Exception as e:
            print(f"⚠️ Failed to generate quality distribution chart: {e}")

        # 3. 效率对比雷达图
        try:
            if len(sessions) > 1:
                # 计算各项效率指标
                metrics = {
                    'Token Efficiency': [],
                    'Time Efficiency': [],
                    'Quality Score': [],
                    'Success Rate': [],
                    'Context Compression': []
                }

                for session in sessions:
                    # Token效率 (越低越好，转换为效率分数)
                    token_efficiency = 1.0 / (session.total_token_usage.total_tokens / 1000) if session.total_token_usage.total_tokens > 0 else 0
                    metrics['Token Efficiency'].append(min(1.0, token_efficiency))

                    # 时间效率 (越快越好)
                    time_efficiency = 1.0 / (session.total_duration / 60) if session.total_duration and session.total_duration > 0 else 0
                    metrics['Time Efficiency'].append(min(1.0, time_efficiency))

                    # 质量分数
                    metrics['Quality Score'].append(session.final_quality_score)

                    # 成功率
                    metrics['Success Rate'].append(1.0 if session.success else 0.0)

                    # 上下文压缩效率
                    metrics['Context Compression'].append(1.0 - session.compression_efficiency if session.compression_efficiency < 1 else 0)

                # 计算平均值
                avg_metrics = {k: statistics.mean(v) for k, v in metrics.items() if v}

                if avg_metrics:
                    # 创建雷达图
                    angles = [n / len(avg_metrics) * 2 * 3.14159 for n in range(len(avg_metrics))]
                    angles += angles[:1]  # 闭合图形

                    values = list(avg_metrics.values())
                    values += values[:1]  # 闭合图形

                    fig, ax = plt.subplots(figsize=(8, 8), subplot_kw=dict(projection='polar'))

                    ax.plot(angles, values, 'o-', linewidth=2, label='System Performance')
                    ax.fill(angles, values, alpha=0.25)

                    ax.set_xticks(angles[:-1])
                    ax.set_xticklabels(avg_metrics.keys())
                    ax.set_ylim(0, 1)

                    plt.title(f'System Efficiency Radar{f" ({task_type})" if task_type else ""}',
                             size=14, y=1.1)
                    plt.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))

                    chart_file = chart_dir / f"efficiency_radar{'_' + task_type if task_type else ''}.png"
                    plt.savefig(chart_file, dpi=300, bbox_inches='tight')
                    plt.close()

                    saved_charts['efficiency_radar'] = str(chart_file)
                    print(f"✅ Efficiency radar chart saved: {chart_file}")

        except Exception as e:
            print(f"⚠️ Failed to generate efficiency radar chart: {e}")

        return saved_charts

    def generate_baseline_comparison_table(self, task_type: Optional[str] = None) -> str:
        """生成基线对比表格，输出"Token ↓ 75%、时间 ↓ 60%"等对照表"""

        if not self.baseline_data:
            return "⚠️ No baseline data loaded. Use load_baseline_data() first."

        comparison = self.compare_with_baseline(task_type)
        if "error" in comparison:
            return f"⚠️ {comparison['error']}"

        # 生成对比表格
        table_lines = [
            "# 📊 Enhanced Multi-Agent System vs Baseline Comparison",
            "",
            f"**Task Type:** {task_type or 'All Tasks'}",
            f"**Comparison Date:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "",
            "## 🎯 Performance Improvements",
            "",
            "| Metric | Baseline | Enhanced | Improvement | Status |",
            "|--------|----------|----------|-------------|--------|"
        ]

        # Token效率对比
        if comparison.get('token_efficiency'):
            for metric_name, data in comparison['token_efficiency'].items():
                improvement = data['improvement_pct']
                status_emoji = "🚀" if improvement > 20 else "📈" if improvement > 0 else "📉" if improvement < -10 else "➡️"

                metric_display = metric_name.replace('_', ' ').title()
                baseline_val = f"{data['baseline']:.0f}"
                current_val = f"{data['current']:.0f}"
                improvement_text = f"{improvement:+.1f}%" if improvement != 0 else "0%"

                table_lines.append(f"| {metric_display} | {baseline_val} | {current_val} | {improvement_text} | {status_emoji} |")

        # 时间效率对比
        if comparison.get('time_efficiency'):
            for metric_name, data in comparison['time_efficiency'].items():
                improvement = data['improvement_pct']
                status_emoji = "🚀" if improvement > 20 else "📈" if improvement > 0 else "📉" if improvement < -10 else "➡️"

                metric_display = metric_name.replace('_', ' ').title()
                baseline_val = f"{data['baseline']:.1f}s"
                current_val = f"{data['current']:.1f}s"
                improvement_text = f"{improvement:+.1f}%" if improvement != 0 else "0%"

                table_lines.append(f"| {metric_display} | {baseline_val} | {current_val} | {improvement_text} | {status_emoji} |")

        # 质量对比
        if comparison.get('quality_metrics'):
            for metric_name, data in comparison['quality_metrics'].items():
                improvement = data['improvement_pct']
                status_emoji = "🚀" if improvement > 10 else "📈" if improvement > 0 else "📉" if improvement < -5 else "➡️"

                metric_display = metric_name.replace('_', ' ').title()
                baseline_val = f"{data['baseline']:.3f}"
                current_val = f"{data['current']:.3f}"
                improvement_text = f"{improvement:+.1f}%" if improvement != 0 else "0%"

                table_lines.append(f"| {metric_display} | {baseline_val} | {current_val} | {improvement_text} | {status_emoji} |")

        # 上下文效率对比
        if comparison.get('context_efficiency'):
            for metric_name, data in comparison['context_efficiency'].items():
                improvement = data['improvement_pct']
                status_emoji = "🚀" if improvement > 30 else "📈" if improvement > 0 else "📉" if improvement < -10 else "➡️"

                metric_display = metric_name.replace('_', ' ').title()
                if 'length' in metric_name:
                    baseline_val = f"{data['baseline']:.0f}"
                    current_val = f"{data['current']:.0f}"
                else:
                    baseline_val = f"{data['baseline']:.3f}"
                    current_val = f"{data['current']:.3f}"
                improvement_text = f"{improvement:+.1f}%" if improvement != 0 else "0%"

                table_lines.append(f"| {metric_display} | {baseline_val} | {current_val} | {improvement_text} | {status_emoji} |")

        # 总体改进摘要
        overall = comparison.get('overall_improvement', {})
        table_lines.extend([
            "",
            "## 📈 Overall Improvement Summary",
            "",
            f"- **Average Improvement:** {overall.get('average_improvement', 0):+.1f}%",
            f"- **Metrics Improved:** {overall.get('positive_improvements', 0)}/{overall.get('total_metrics', 0)} ({overall.get('improvement_rate', 0):.1f}%)",
            ""
        ])

        # 关键亮点
        highlights = []
        if comparison.get('token_efficiency', {}).get('tokens_per_session', {}).get('improvement_pct', 0) < -50:
            highlights.append("🎯 **Token Usage Reduced by >50%** - Significant efficiency gain")

        if comparison.get('time_efficiency', {}).get('duration_per_session', {}).get('improvement_pct', 0) < -40:
            highlights.append("⚡ **Processing Time Reduced by >40%** - Major speed improvement")

        if comparison.get('quality_metrics', {}).get('quality_score', {}).get('improvement_pct', 0) > 15:
            highlights.append("🏆 **Quality Score Improved by >15%** - Better output quality")

        if highlights:
            table_lines.extend([
                "## 🌟 Key Highlights",
                ""
            ])
            table_lines.extend(highlights)
            table_lines.append("")

        # 建议
        recommendations = []
        avg_improvement = overall.get('average_improvement', 0)

        if avg_improvement > 25:
            recommendations.append("✅ **Excellent Performance** - System significantly outperforms baseline")
        elif avg_improvement > 10:
            recommendations.append("👍 **Good Performance** - System shows solid improvements over baseline")
        elif avg_improvement > 0:
            recommendations.append("📊 **Moderate Performance** - System shows some improvements, consider further optimization")
        else:
            recommendations.append("⚠️ **Performance Review Needed** - System underperforming baseline, investigate issues")

        if recommendations:
            table_lines.extend([
                "## 💡 Recommendations",
                ""
            ])
            table_lines.extend(recommendations)

        return "\n".join(table_lines)

    def save_baseline_comparison_report(self, task_type: Optional[str] = None) -> Optional[str]:
        """保存基线对比报告"""

        if not self.baseline_data:
            print("⚠️ No baseline data loaded")
            return None

        report_content = self.generate_baseline_comparison_table(task_type)

        # 生成文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        task_suffix = f"_{task_type}" if task_type else "_all"
        report_file = self.output_dir / f"baseline_comparison{task_suffix}_{timestamp}.md"

        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report_content)

            # 同时生成可视化图表
            charts = self.generate_visualization_charts(task_type)

            print(f"✅ Baseline comparison report saved: {report_file}")
            if charts:
                print(f"📊 Generated {len(charts)} visualization charts")

            return str(report_file)

        except Exception as e:
            print(f"❌ Failed to save baseline comparison report: {e}")
            return None
