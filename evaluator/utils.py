"""
Core evaluation functionality - combines utilities and main logic
"""

import argparse
import asyncio
import json
import os
from typing import Dict, List, Any, Optional


# Utility functions
def load_jsonl(file_path: str) -> List[Dict[str, Any]]:
    """Load data from a JSONL file"""
    data = []
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line:
                data.append(json.loads(line))
    return data


def load_results(file_path: str) -> Dict[str, Any]:
    """Load results from a JSON file"""
    with open(file_path, 'r', encoding='utf-8') as f:
        return json.load(f)


def resolve_answers_file(results_file: str, use_answers: bool = True) -> str:
    """Resolve the path to the answers file if use_answers is True"""
    if not use_answers or results_file.endswith('_answers.json'):
        return results_file

    # Get project root directory
    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

    # Try to find answers file in results/answers/ directory first
    base_file = os.path.basename(results_file).replace('.json', '_answers.json')
    answers_dir = os.path.join(project_root, 'results', 'answers')
    answers_path = os.path.join(answers_dir, base_file)

    if os.path.exists(answers_path):
        return answers_path

    # Also try results_baseline/answers/ directory for baseline results
    baseline_answers_dir = os.path.join(project_root, 'results_baseline', 'answers')
    baseline_answers_path = os.path.join(baseline_answers_dir, base_file)

    if os.path.exists(baseline_answers_path):
        return baseline_answers_path

    # Fallback: try same directory with _answers suffix
    fallback_path = results_file.replace('.json', '_answers.json')
    return fallback_path if os.path.exists(fallback_path) else results_file


def extract_result_items(results: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Extract result items from results data structure"""
    if "results" in results:
        return results["results"]
    elif "problems" in results:
        return list(results["problems"].values())
    else:
        return results


def get_evaluator_mapping(output_dir: str) -> Dict[str, Any]:
    """Get mapping of dataset names to evaluator instances"""
    from .gsm8k_evaluator import GSM8KEvaluator
    from .math_evaluator import MathEvaluator
    from .mbpp_evaluator import MBPPEvaluator
    from .humaneval_evaluator import HumanEvalEvaluator
    from .drop_evaluator import DropEvaluator
    from .hotpotqa_evaluator import HotpotQAEvaluator
    from .strategyqa_evaluator import StrategyQAEvaluator
    from .gpqa_evaluator import GPQAEvaluator
    from .mmlu_evaluator import MMLUEvaluator

    return {
        "gsm8k": GSM8KEvaluator(output_dir),
        "math": MathEvaluator(output_dir),
        "mbpp": MBPPEvaluator(output_dir),
        "humaneval": HumanEvalEvaluator(output_dir),
        "drop": DropEvaluator(output_dir),
        "hotpotqa": HotpotQAEvaluator(output_dir),
        "strategyqa": StrategyQAEvaluator(output_dir),
        "gpqa": GPQAEvaluator(output_dir),
        "mmlu": MMLUEvaluator(output_dir)
    }


# Main evaluation logic
class EvaluationRunner:
    """Main evaluation runner class"""

    def __init__(self, args: argparse.Namespace):
        self.args = args
        self.results_file = resolve_answers_file(args.results_file, args.use_answers)
        self.output_dir = args.output_dir
        self.benchmark_dir = args.benchmark_dir
        self.verbose = args.verbose

        os.makedirs(self.output_dir, exist_ok=True)
        self.evaluators = get_evaluator_mapping(self.output_dir)

    def get_datasets_to_evaluate(self) -> List[str]:
        """Get list of datasets to evaluate"""
        return list(self.evaluators.keys()) if self.args.dataset == "all" else [self.args.dataset]

    async def run_single_evaluation(self, dataset: str) -> bool:
        """Run evaluation for a single dataset"""
        benchmark_file = os.path.join(self.benchmark_dir, f"{dataset}.jsonl")

        if not os.path.exists(benchmark_file):
            print(f"Benchmark file not found: {benchmark_file}")
            return False

        if self.verbose:
            print(f"Loading benchmark data from {benchmark_file}")

        try:
            problems = load_jsonl(benchmark_file)
            results = load_results(self.results_file)
            result_items = extract_result_items(results)

            evaluator = self.evaluators[dataset]
            print(f"Evaluating {dataset} with {len(problems)} problems")

            await evaluator.run_evaluation(
                problems=problems,
                results=result_items,
                prefix=os.path.basename(self.results_file).split('.')[0]
            )

            print(f"Completed evaluation for {dataset.upper()}")
            return True

        except Exception as e:
            print(f"Error during evaluation of {dataset}: {e}")
            if self.verbose:
                import traceback
                traceback.print_exc()
            return False

    async def run_all_evaluations(self) -> None:
        """Run evaluations for all specified datasets"""
        if self.args.use_answers:
            print(f"Using answers-only file: {self.results_file}")

        print(f"Loading results from {self.results_file}")

        datasets_to_evaluate = self.get_datasets_to_evaluate()

        if self.verbose:
            print(f"Datasets to evaluate: {', '.join(datasets_to_evaluate)}")

        success_count = 0
        total_count = len(datasets_to_evaluate)

        for dataset in datasets_to_evaluate:
            if await self.run_single_evaluation(dataset):
                success_count += 1
            print()

        # Print summary
        print("="*50)
        print("EVALUATION SUMMARY")
        print("="*50)
        print(f"Total datasets: {total_count}")
        print(f"Successful evaluations: {success_count}")
        print(f"Failed evaluations: {total_count - success_count}")

        if success_count == total_count:
            print("✅ All evaluations completed successfully!")
        elif success_count > 0:
            print("⚠️  Some evaluations completed with errors")
        else:
            print("❌ All evaluations failed")


def create_argument_parser() -> argparse.ArgumentParser:
    """Create and configure argument parser"""
    parser = argparse.ArgumentParser(
        description="Evaluate model outputs against benchmark datasets",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python evaluate.py --results-file results/gsm8k_result.json --dataset gsm8k
  python evaluate.py --results-file results/full_result.json --dataset all --no-use-answers
  python evaluate.py --results-file results/math_result.json --dataset math --verbose
        """
    )

    parser.add_argument("--results-file", required=True, help="Path to results JSON file")
    parser.add_argument("--dataset", choices=["gsm8k", "math", "mbpp", "humaneval", "drop", "hotpotqa", "strategyqa", "gpqa", "mmlu", "all"],
                       required=True, help="Dataset to evaluate")
    parser.add_argument("--benchmark-dir", default="benchmark",
                       help="Directory containing benchmark datasets (default: benchmark)")
    parser.add_argument("--output-dir", default="results",
                       help="Directory to save evaluation results (default: results)")
    parser.add_argument("--verbose", action="store_true", help="Print verbose output")
    parser.add_argument("--use-answers", action="store_true", dest="use_answers",
                       help="Use answers-only file (default: enabled)")
    parser.add_argument("--no-use-answers", action="store_false", dest="use_answers",
                       help="Do NOT use answers-only file")

    parser.set_defaults(use_answers=True)
    return parser


async def main(args: Optional[argparse.Namespace] = None) -> None:
    """Main evaluation function"""
    if args is None:
        parser = create_argument_parser()
        args = parser.parse_args()

    runner = EvaluationRunner(args)
    await runner.run_all_evaluations()
