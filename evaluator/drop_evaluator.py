import re
import json
from typing import Any, Dict, List, Tuple, Optional
from .base_evaluator import BaseEvaluator

class DropEvaluator(BaseEvaluator):
    def __init__(self, log_path: str = "results"):
        super().__init__("drop", log_path)

    def get_result_columns(self) -> List[str]:
        return ["problem", "prediction", "expected_output", "score", "execution_time", "extracted_output"]

    def normalize_text(self, text: str) -> str:
        """Normalize text for comparison"""
        if not text:
            return ""
        
        # Convert to lowercase and strip
        text = str(text).lower().strip()
        
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove common punctuation at the end
        text = re.sub(r'[.!?;,]+$', '', text)
        
        return text

    def extract_number_from_text(self, text: str) -> Optional[float]:
        """Extract number from text"""
        if not text:
            return None
            
        # Try to find numbers in the text
        numbers = re.findall(r'-?\d+\.?\d*', str(text))
        
        if numbers:
            try:
                return float(numbers[0])
            except ValueError:
                pass
        
        return None

    def calculate_score(self, expected_output: Any, prediction: Any) -> Tuple[float, Any]:
        """Calculate score for DROP evaluation with multi-answer support"""
        if not prediction:
            return 0.0, None
            
        expected_str = str(expected_output).strip()
        prediction_normalized = self.normalize_text(str(prediction))
        
        # Handle multiple acceptable answers (separated by |)
        if '|' in expected_str:
            expected_answers = [ans.strip() for ans in expected_str.split('|')]
        else:
            expected_answers = [expected_str]
        
        # Check against each acceptable answer
        for expected_ans in expected_answers:
            expected_normalized = self.normalize_text(expected_ans)
            
            # Exact match
            if expected_normalized == prediction_normalized:
                return 1.0, prediction_normalized
            
            # Try numerical comparison
            expected_num = self.extract_number_from_text(expected_normalized)
            predicted_num = self.extract_number_from_text(prediction_normalized)
            
            if expected_num is not None and predicted_num is not None:
                if abs(expected_num - predicted_num) < 0.001:  # Allow for small floating point errors
                    return 1.0, predicted_num
            
            # Check if predicted text contains the expected answer
            if expected_normalized in prediction_normalized:
                return 1.0, prediction_normalized
                
            # Check for partial matches for text answers
            if len(expected_normalized) > 3 and expected_normalized in prediction_normalized:
                return 1.0, prediction_normalized
        
        return 0.0, prediction_normalized

    async def evaluate_problem(self, problem: Dict[str, Any], results: Dict[str, Any]) -> Tuple[Any, ...]:
        """Evaluate a single DROP problem - handles both real and fake formats"""

        # Parse real DROP format if needed
        parsed_problem = self.parse_real_drop_format(problem)

        # Handle results being a list or dict
        if isinstance(results, list):
            results_list = results
        else:
            results_list = results.get("results", [])

        # Find the matching result
        result = self.find_matching_result(parsed_problem, results_list)
        
        if not result:
            self.log_warning(f"No matching result found for problem: {parsed_problem.get('question', 'Unknown')[:50]}...")
            return (
                parsed_problem.get("question", ""),
                "",
                parsed_problem.get("answer", ""),
                0.0,
                0.0,
                ""
            )

        # Get the prediction and expected answer
        prediction = result.get("solution", "")
        expected_answer = parsed_problem.get("answer", "")
        
        # Calculate score
        score, extracted_output = self.calculate_score(expected_answer, prediction)
        
        # Get execution time
        execution_time = self.get_execution_cost(result)
        
        # Log mismatches for debugging
        if score < 1.0:
            self.log_mismatch(
                parsed_problem.get("question", ""),
                expected_answer,
                prediction,
                extracted_output
            )

        return (
            parsed_problem.get("question", ""),
            prediction,
            expected_answer,
            score,
            execution_time,
            extracted_output
        )

    def parse_real_drop_format(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Parse real DROP format and convert to expected format"""

        if 'context' in data and 'ref_text' in data:
            # Real DROP format
            context = data['context']

            # Parse context to extract passage and question
            if 'Passage:' in context and 'Question:' in context:
                parts = context.split('Question:')
                if len(parts) == 2:
                    passage = parts[0].replace('Passage:', '').strip()
                    question = parts[1].replace('Answer:', '').strip()
                else:
                    passage = context
                    question = "Unknown question"
            else:
                passage = context
                question = "Unknown question"

            # Use ref_text as the ground truth answer
            answer = data.get('ref_text', '').strip()

            return {
                'id': data.get('id', f'drop_{hash(context)}'),
                'passage': passage,
                'question': question,
                'answer': answer,
                'problem': f"Passage: {passage}\n\nQuestion: {question}",
                'metadata': {
                    'original_completion': data.get('completion', ''),
                    'dataset': 'drop_real'
                }
            }
        else:
            # Original format - return as is
            return data

    def find_matching_result(self, problem: Dict[str, Any], results: List[Dict[str, Any]],
                           problem_key: str = "question", result_key: str = "prompt") -> Optional[Dict[str, Any]]:
        """Find matching result for DROP problem - handles both real and fake formats"""

        # Parse real DROP format if needed
        parsed_problem = self.parse_real_drop_format(problem)

        # Get the question from parsed problem
        question = parsed_problem.get("question", "")
        passage = parsed_problem.get("passage", "")

        if not question:
            return None

        # First try exact question match
        for result in results:
            result_prompt = result.get("prompt", "")
            if question in result_prompt:
                return result

        # Try to match with passage + question combination
        if passage:
            for result in results:
                result_prompt = result.get("prompt", "")
                if question in result_prompt and passage[:100] in result_prompt:
                    return result

        # Fallback to base class method
        return super().find_matching_result(parsed_problem, results, problem_key, result_key)