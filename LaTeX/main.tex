%File: anonymous-submission-latex-2026.tex
\documentclass[letterpaper]{article} % DO NOT CHANGE THIS
\usepackage[submission]{aaai2026}  % DO NOT CHANGE THIS
\usepackage{times}  % DO NOT CHANGE THIS
\usepackage{helvet}  % DO NOT CHANGE THIS
\usepackage{courier}  % DO NOT CHANGE THIS
\usepackage[hyphens]{url}  % DO NOT CHANGE THIS
\usepackage{graphicx} % DO NOT CHANGE THIS
\urlstyle{rm} % DO NOT CHANGE THIS
\def\UrlFont{\rm}  % DO NOT CHANGE THIS
\usepackage{natbib}  % DO NOT CHANGE THIS AND DO NOT ADD ANY OPTIONS TO IT
\usepackage{caption} % DO NOT CHANGE THIS AND DO NOT ADD ANY OPTIONS TO IT
\frenchspacing  % DO NOT CHANGE THIS
\setlength{\pdfpagewidth}{8.5in} % DO NOT CHANGE THIS
\setlength{\pdfpageheight}{11in} % DO NOT CHANGE THIS
%
% These are recommended to typeset algorithms but not required. See the subsubsection on algorithms. Remove them if you don't have algorithms in your paper.
\usepackage{algorithm}
\usepackage{algorithmic}
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage{amsthm}
\newtheorem{lemma}{Lemma}
\newtheorem{theorem}{Theorem}

%
% These are are recommended to typeset listings but not required. See the subsubsection on listing. Remove this block if you don't have listings in your paper.
\usepackage{newfloat}
\usepackage{listings}
\DeclareCaptionStyle{ruled}{labelfont=normalfont,labelsep=colon,strut=off} % DO NOT CHANGE THIS
\lstset{%
	basicstyle={\footnotesize\ttfamily},% footnotesize acceptable for monospace
	numbers=left,numberstyle=\footnotesize,xleftmargin=2em,% show line numbers, remove this entire line if you don't want the numbers.
	aboveskip=0pt,belowskip=0pt,%
	showstringspaces=false,tabsize=2,breaklines=true}
\floatstyle{ruled}
\newfloat{listing}{tb}{lst}{}
\floatname{listing}{Listing}
%
% Keep the \pdfinfo as shown here. There's no need
% for you to add the /Title and /Author tags.
\pdfinfo{
/TemplateVersion (2026.1)
}

% DISALLOWED PACKAGES
% \usepackage{authblk} -- This package is specifically forbidden
% \usepackage{balance} -- This package is specifically forbidden
% \usepackage{color (if used in text)
% \usepackage{CJK} -- This package is specifically forbidden
% \usepackage{float} -- This package is specifically forbidden
% \usepackage{flushend} -- This package is specifically forbidden
% \usepackage{fontenc} -- This package is specifically forbidden
% \usepackage{fullpage} -- This package is specifically forbidden
% \usepackage{geometry} -- This package is specifically forbidden
% \usepackage{grffile} -- This package is specifically forbidden
% \usepackage{hyperref} -- This package is specifically forbidden
% \usepackage{navigator} -- This package is specifically forbidden
% (or any other package that embeds links such as navigator or hyperref)
% \indentfirst} -- This package is specifically forbidden
% \layout} -- This package is specifically forbidden
% \multicol} -- This package is specifically forbidden
% \nameref} -- This package is specifically forbidden
% \usepackage{savetrees} -- This package is specifically forbidden
% \usepackage{setspace} -- This package is specifically forbidden
% \usepackage{stfloats} -- This package is specifically forbidden
% \usepackage{tabu} -- This package is specifically forbidden
% \usepackage{titlesec} -- This package is specifically forbidden
% \usepackage{tocbibind} -- This package is specifically forbidden
% \usepackage{ulem} -- This package is specifically forbidden
% \usepackage{wrapfig} -- This package is specifically forbidden
% DISALLOWED COMMANDS
% \nocopyright -- Your paper will not be published if you use this command
% \addtolength -- This command may not be used
% \balance -- This command may not be used
% \baselinestretch -- Your paper will not be published if you use this command
% \clearpage -- No page breaks of any kind may be used for the final version of your paper
% \columnsep -- This command may not be used
% \newpage -- No page breaks of any kind may be used for the final version of your paper
% \pagebreak -- No page breaks of any kind may be used for the final version of your paperr
% \pagestyle -- This command may not be used
% \tiny -- This is not an acceptable font size.
% \vspace{- -- No negative value may be used in proximity of a caption, figure, table, section, subsection, subsubsection, or reference
% \vskip{- -- No negative value may be used to alter spacing above or below a caption, figure, table, section, subsection, subsubsection, or reference

\setcounter{secnumdepth}{0} %May be changed to 1 or 2 if section numbers are desired.

% The file aaai2026.sty is the style file for AAAI Press
% proceedings, working notes, and technical reports.
%

% Title
\title{Efficient Multi-Agent Collaboration through Draft-Mediated Communication: Addressing Context Engineering Challenges in LLM Cooperation}
\author{
    Anonymous Submission
}
\affiliations{
    Anonymous Institution
}

\begin{document}

\maketitle

% --------- Polished Abstract ---------
\begin{abstract}
Multi-agent systems based on Large Language Models (LLMs) often suffer from severe inefficiencies, as direct dialogue leads to context explosion and prohibitive costs. We introduce Draft-Mediated Collaboration (DMC), a novel communication protocol that replaces verbose dialogue with structured, parallel annotations on a shared, concise draft. Governed by an adaptive merger and a context compressor, DMC maintains the benefits of multi-perspective reasoning while enforcing a strict token economy. Across nine diverse benchmarks, DMC achieves competitive or superior accuracy to strong baselines while reducing token consumption by 60--80\%, demonstrating that artifact-centric interaction is a more scalable paradigm for multi-agent collaboration.
\end{abstract}

% --------- Introduction (already polished) ---------
\section{Introduction}

The capacity of Large Language Models (LLMs) to act as autonomous agents has unlocked new possibilities for solving complex, multi-faceted problems \cite{brown2020language, ouyang2022training}. A natural evolution is to form teams of LLM agents, hoping to harness collective intelligence. However, the predominant collaboration paradigm—direct, open-ended dialogue—faces a critical scalability bottleneck that undermines its potential. As agents converse, the shared context expands uncontrollably, leading to four fundamental challenges: 1)~\textbf{Context Explosion}, where history quickly exceeds model limits; 2)~\textbf{Topic Drift}, where conversations lose focus; 3)~\textbf{Computational Inefficiency}, with costs scaling quadratically with dialogue length; and 4)~\textbf{Information Dilution}, where key insights are buried in verbose exchanges.

Existing approaches offer an unsatisfactory trade-off. On one hand, dialogue-based systems like multi-agent debate \cite{du2023improving, chen2023multi} demonstrate the power of multiple perspectives but incur unsustainable computational overhead. Much of the recent work on LLM reasoning, such as Chain-of-Thought~\cite{wei2022chain} and its more complex variants like Tree-of-Thoughts~\cite{yao2024tree}, has focused on structuring the internal thought process of a *single agent*. However, when multiple agents collaborate, the bottleneck shifts from individual reasoning to inter-agent communication. We argue that optimizing the communication protocol is as critical as optimizing the reasoning topology. Just as an effective human team relies on a concise, shared document (like a meeting minute or a project draft) rather than verbose, unstructured discussion, we posit that multi-agent LLM systems can achieve greater efficiency by shifting their interaction model from dialogue to a shared artifact.

To bridge this gap, we propose a fundamentally different approach: \textbf{Draft-Mediated Collaboration (DMC)}. Instead of agents communicating directly *with each other*, they interact indirectly *through a shared draft*. This draft serves as a centralized, structured artifact that embodies the current state of the solution. Communication shifts from verbose natural language exchanges to concise, targeted operations on the draft, such as proposing edits, adding annotations, or verifying facts. This paradigm inherently enforces focus, structures interaction, and contains context growth.

Our work makes the following contributions, implemented and validated in our accompanying open-source framework:
\begin{itemize}
    \item \textbf{A Draft-Mediated Communication Protocol:} We formalize and implement a novel protocol where a shared draft, not dialogue, is the central medium of collaboration, fundamentally altering the dynamics of multi-agent interaction.
    
    \item \textbf{Efficient Specialised Agent Roles:} We design a lean set of agents—including Workers, a Merger, and a Leader—each with a specialised function that contributes to the draft's evolution without redundant communication. This is orchestrated by a central \textbf{Controller}, as depicted in Figure~\ref{fig:architecture}.
    
    \item \textbf{Dynamic Efficiency Mechanisms:} We introduce two key components to actively manage computational costs: a \textbf{Dynamic Context Compressor} that summarizes conversational history before each iteration, and an \textbf{Adaptive Merge Strategy Selector} that chooses the most efficient synthesis method based on feedback coherence.
    
    \item \textbf{Comprehensive Empirical Validation:} We conduct extensive experiments on nine diverse benchmarks, demonstrating that DMC not only achieves higher accuracy than prominent baselines (including CoT, CoD, and LLM-Debate) but also reduces token usage by over 70\%.
\end{itemize}

This work establishes that for complex problem-solving, the most effective multi-agent collaboration is not the most loquacious. By structuring interaction around a shared artifact, DMC provides a scalable and efficient path forward for multi-agent LLM systems.

% Figure for system architecture
\begin{figure}[t]
\centering
% --- Mermaid diagram placeholder ---
% The diagram generated previously should be converted to a PDF 
% and included here using \includegraphics
\fbox{
    \parbox{0.9\columnwidth}{
        \centering
        \vspace{4cm}
        \textbf{Figure 1: System Architecture Diagram} \\
        \textit{(Placeholder for the Mermaid diagram showing the Controller, Worker, Merger, and Leader agent interactions, and the iterative refinement loop with context compression.)}
        \vspace{4cm}
    }
}
\caption{The Draft-Mediated Collaboration (DMC) framework. The Controller orchestrates an iterative loop where Worker agents generate and annotate a shared draft. The Merger synthesizes feedback, and the Leader evaluates quality. A Context Compressor prunes history between rounds to ensure efficiency.}
\label{fig:architecture}
\end{figure}


\section{Related Work}

\textbf{Context Engineering in LLMs:} Recent advances have focused on leveraging longer contexts effectively \cite{brown2020language}. However, longer contexts introduce challenges including attention dilution, increased computational costs, and difficulty maintaining focus on specific tasks. Our work addresses these challenges specifically in multi-agent settings.

\textbf{Multi-Agent LLM Collaboration:} Existing approaches typically involve direct agent communication through extended dialogues \cite{chen2023multi,du2023improving}. While these methods can improve reasoning quality, they suffer from context explosion and high computational costs. Chen et al. \cite{chen2023multi} demonstrated multi-agent debate effectiveness but acknowledged the computational overhead of lengthy exchanges.
Recent variants further explore structured reasoning topologies, including Tree\nobreakdash-of\nobreakdash-Thoughts~\cite{yao2024tree} and Graph\nobreakdash-of\nobreakdash-Thoughts~\cite{besta2024graph}. Automated workflow discovery like AFLOW~\cite{zhang2024aflow} and decentralised agent collectives such as GPT\nobreakdash-Swarm~\cite{frendo2023gptswarm} offer orthogonal perspectives on orchestrating multiple LLM calls.

\textbf{Structured Communication in AI:} Classical multi-agent systems \cite{stone2000multiagent,wooldridge2009introduction} have explored structured communication protocols, but these approaches were designed for symbolic reasoning rather than natural language collaboration. Our work bridges structured communication principles with modern LLM capabilities.

\textbf{Efficiency in LLM Applications:} Recent work has emphasized the importance of computational efficiency in LLM applications. Our draft-mediated approach directly addresses efficiency concerns while maintaining collaborative benefits.

% ---------- NEW SECTION: Problem Definition ----------
\section{Problem Definition}
We formalise multi\nobreakdash-agent collaboration under strict context budgets as the following optimisation problem.  Let $q$ denote a natural\nobreakdash-language task prompt and $A=\{a_1,\dots,a_{|A|}\}$ the set of LLM agents.  The system maintains a shared draft $d^{(r)}$ at round $r$.  Each round proceeds in three stages:
\begin{enumerate}
    \item \textbf{Broadcast} $d^{(r)}$ to all agents (no personalised context).
    \item Each agent produces an \emph{annotation} $\alpha_i^{(r)}$ whose length satisfies $\|\alpha_i^{(r)}\|\le L$.
    \item A deterministic merge function $\mathcal M$ synthesises a new draft $d^{(r{+}1)}=\mathcal M\bigl(d^{(r)},\{\alpha_i^{(r)}\}\bigr)$.
\end{enumerate}
The collaboration halts after $R$ rounds or once a quality estimator $\mathcal Q(d^{(r)})\ge\tau$.  The objective is to maximise expected answer quality while respecting a global token budget $B$:
\[
\max_{A,\,\mathcal M,\,\mathcal Q}\; \mathbb E_{q\sim\mathcal D}[\text{Acc}(d^{(\le R)},q)]\quad\text{s.t. }\sum_{r=1}^{R}\bigl(N + |A|L\bigr)\le B.
\]
Here $N=\|d^{(r)}\|$ and the inequality uses Theorem~\ref{thm:total}.  This formulation highlights two design axes: (i)~\emph{draft length} $N$ and (ii)~\emph{per\nobreakdash-agent annotation cap} $L$.  DMC fixes $N\approx120$ and $L\le50$ in practice, yielding a predictable linear budget.  Contrastingly, dialogue-based schemes lack such upper bounds because message chains grow with both $A$ and $R$.

\section{Method}

Our draft-mediated collaboration framework addresses context engineering challenges through structured, indirect agent communication. Instead of lengthy multi-agent dialogues, agents collaborate around concise drafts using targeted annotations and intelligent synthesis.

\subsection{Draft-Mediated Communication Architecture}

\subsubsection{Core Design Principles}
Our framework is built on three key principles that address context engineering challenges:

\textbf{Context Containment}: Each collaboration phase operates within bounded context limits, preventing exponential growth typical in dialogue-based approaches.

\textbf{Information Density}: Communication focuses on specific, actionable content rather than verbose explanations, maximizing information value per token.

\textbf{Structured Interaction}: Agents interact through predefined formats (drafts, annotations, evaluations) rather than open-ended conversations, maintaining focus and efficiency.

\subsubsection{Draft as Communication Medium}
The central innovation is using concise drafts as communication intermediates. Unlike traditional approaches where agents directly exchange lengthy messages, our agents collaborate by:

\begin{itemize}
\item \textit{Creating focused drafts}: Initial solutions are generated concisely, typically 100-300 tokens depending on task complexity
\item \textit{Providing targeted annotations}: Improvements are suggested through specific, bounded annotations rather than lengthy explanations
\item \textit{Enabling indirect collaboration}: Agents see each other's work through the shared draft, not through direct communication
\end{itemize}

This design eliminates the context explosion problem while preserving the collaborative benefits of multiple perspectives.

\subsection{Efficient Agent Specialization}

\subsubsection{Role-Based Collaboration}
To maximize efficiency while maintaining quality, each agent is assigned a well-defined responsibility. The \textit{Domain Expert} contributes task-specific knowledge with minimal context overhead, the \textit{Quality Assessor} verifies completeness and correctness through structured criteria, the \textit{Fact Checker} performs targeted verification of key statements, and the \textit{Editor} polishes clarity and presentation. Clear boundaries among these roles prevent scope creep and keep the collaborative focus sharp.

\subsubsection{Structured Annotation System}
Instead of verbose free-form discussions, our annotation system enforces a concise feedback protocol: every annotation pinpoints the exact text span to revise, labels the suggestion category (accuracy, clarity, or completeness), assigns a priority level, and is limited to 50–100 tokens. This structure concentrates the agents’ attention on high-impact edits while keeping context growth under control.

\subsection{Intelligent Synthesis Without Dialogue}

\subsubsection{LLM-Driven Semantic Merging}
The Merger agent integrates content without the overhead of extended debates. It performs semantic analysis to understand relationships among solution components, detects contradictions, resolves conflicts through reasoning, and finally synthesises a coherent answer that combines multiple perspectives.

\subsubsection{Context-Aware Quality Assessment}
The Leader agent applies multi-dimensional scoring—covering accuracy, completeness, and clarity—then returns focused feedback. This targeted guidance steers subsequent refinement rounds without triggering a full re-discussion.

\subsection{Adaptive Collaboration Intensity}

\subsubsection{Task-Complexity-Based Scaling}
Our framework automatically adjusts collaboration depth based on task requirements, optimizing the efficiency-quality tradeoff:

\textbf{Minimal Collaboration} (Simple tasks): Single-round annotation with basic quality checks, typically requiring <500 total tokens.

\textbf{Standard Collaboration} (Moderate tasks): Full annotation cycle with semantic merging, using 800-1200 tokens.

\textbf{Enhanced Collaboration} (Complex tasks): Multiple refinement rounds with quality-driven iteration, capped at 2000 tokens.

This adaptive approach ensures computational efficiency while maintaining quality for tasks that require deeper collaboration.

\subsubsection{Domain-Specific Optimization}
We implement task-specific efficiency optimizations:

\textbf{Mathematical Reasoning}: Focus on step verification and numerical accuracy with minimal explanatory text.

\textbf{Code Generation}: Emphasize functional correctness and edge case handling through structured testing protocols.

\textbf{Reading Comprehension}: Leverage evidence extraction and logical inference with targeted fact verification.

\textbf{Knowledge Reasoning}: Implement systematic option evaluation with focused knowledge application.

Each domain optimization reduces irrelevant content while maintaining task-specific quality requirements.

\subsection{Computational Efficiency Mechanisms}

\subsubsection{Token Usage Optimization}
We minimise computational overhead through four complementary techniques. First, each interaction phase is subject to strict token limits to keep communication bounded. Second, reusable prompt templates eliminate redundant instruction tokens. Third, progressive refinement encourages agents to improve drafts incrementally rather than regenerate them from scratch. Finally, an early-termination rule stops the collaboration once the solution exceeds quality thresholds, avoiding unnecessary LLM calls.

\subsubsection{Context Management}
Efficiency is further improved by propagating only essential information across rounds, compressing historical context into concise summaries, and presenting different abstraction levels to different agent roles as needed.

% ===== 新增 Implementation Details 部分 =====
\section{Implementation Details}

Our framework is implemented in Python and orchestrates multiple LLM providers through asynchronous \texttt{asyncio} APIs. Instead of relying on specific hardware, our experiments leverage a suite of publicly available models to ensure reproducibility. The primary models used across different agent roles include OpenAI's \texttt{gpt-4o-mini}, Anthropic's \texttt{claude-3-5-haiku}, \texttt{deepseek-chat}, and Alibaba's \texttt{qwen-plus-latest}. For certain tasks, we also incorporate \texttt{llama-3.3-70b} (via Groq) and Google's \texttt{gemini-2.0-flash}. This heterogeneous setup allows us to validate the robustness and generalisability of our communication protocol across different model architectures and capabilities. Key system parameters, such as retry attempts and token budgets, are detailed in Table~\ref{tab:sysparams}.

\subsection{System Parameters}
Key parameters include maximum concurrency (\textbf{20} parallel completions), retry attempts (\textbf{6}), and a token budget of \textbf{4096} tokens per request. We limit collaboration to at most \textbf{3} agents and \textbf{2} rounds for efficiency.

\begin{table}[t]
\centering
\caption{Key system parameters.}
\label{tab:sysparams}
\begin{tabular}{lcc}
\hline
\textbf{Parameter} & \textbf{Value} & \textbf{Source} \\
\hline
Max concurrency & 20 & Config \\
Retry attempts & 6 & Config \\
Token budget & 4096 & Config \\
Quality threshold & 0.8 & Config \\
\hline
\end{tabular}
\end{table}

\subsection{Algorithm Overview}
Algorithm~\ref{alg:dmc} presents the simplified draft-mediated collaboration loop used in most experiments.

\begin{algorithm}[t]
\caption{Draft-Mediated Collaboration (Simplified Controller)}
\label{alg:dmc}
\begin{algorithmic}[1]
\REQUIRE Task prompt $q$, task type $t$, agent list $A$
\STATE Initialize empty draft $d \leftarrow \emptyset$
\STATE \textbf{parallel} for $a \in A$ \textbf{do}
\STATE \quad $d_a \leftarrow$ \textsc{GenerateDraft}$(a,q,t)$
\STATE \textbf{end parallel}
\STATE $d \leftarrow$ \textsc{SelectBestDraft}$\big(\{d_a\}\big)$
\STATE \textbf{parallel} for $a \in A$ \textbf{do}
\STATE \quad $\alpha_a \leftarrow$ \textsc{AnnotateDraft}$(a,d)$
\STATE \textbf{end parallel}
\STATE $d \leftarrow$ \textsc{MergeAgent}$\big(d,\{\alpha_a\}\big)$
\STATE $q_s \leftarrow$ \textsc{LeaderEvaluate}$(d)$
\IF{$q_s < \tau$}
\STATE Provide feedback and repeat Lines~2--10 (max 2 rounds)
\ENDIF
\RETURN Final answer extracted from $d$
\end{algorithmic}
\end{algorithm}

% ---------- NEW SUBSECTION: Collaboration Workflow Details ----------
\subsection{Collaboration Workflow Details}

The entire DMC protocol revolves around five core \textbf{communication primitives} orchestrated by the Controller:
\begin{description}[leftmargin=0pt,labelsep=0.5em,style=nextline]
  \item[\textbf{GenerateDraft$(q)$}] creates initial solution drafts in parallel.
  \item[\textbf{Annotate$(d)$}] agents add structured feedback to the shared draft.
  \item[\textbf{Merge$(d,\{\alpha\})$}] synthesises the annotations into an updated draft.
  \item[\textbf{Evaluate$(d)$}] the Leader agent assesses draft quality for early termination.
  \item[\textbf{Compress$(h)$}] prunes historical context between rounds to stay within budget.
\end{description}
The detailed interaction among these primitives is described below.

Figure~\ref{fig:workflow} and Algorithm~\ref{alg:dmc} together depict the full life cycle of a DMC session.  We elaborate each phase below, emphasising agent responsibilities, concurrency, and token accounting.

\paragraph{Phase~0: Parallel Draft Generation.}  The Controller issues the raw task prompt $q$ to each Worker ($a_i\in A$) using a role-specific system prompt.  All calls occur concurrently, so the wall\nobreakdash-clock cost equals the slowest Worker latency.  Each Worker returns a concise solution draft $d_i$ (at most $N_0$ tokens).  The Controller then selects the best candidate via automatic scoring (e.g., exact match for math/code, or a lightweight voting LLM) to obtain the base draft $d^{(1)}$.

\paragraph{Phase~1: Broadcast \textbf{Shared Draft}.}  The selected draft is broadcast verbatim to every Worker.  Since context is shared, this incurs exactly $N$ input tokens per agent, independent of $R$.

\paragraph{Phase~2: Structured Annotation.}  Each Worker inspects $d^{(r)}$ and emits a \emph{bounded} JSON annotation list that follows a strict schema. This schema is central to DMC's efficiency because it converts free-form feedback into machine-readable operations. Listing~\ref{lst:gsm8k_json} shows a brief example from our GSM8K experiments (the extra field \texttt{span\_id} is added for clarity).

\begin{listing}[tb]
\caption{Example JSON annotation (GSM8K)}\label{lst:gsm8k_json}
\begin{lstlisting}[language=json,numbers=none]
[
  {"span_id": 0, "type": "completeness", "priority": 3,
   "suggestion": "Consider adding a brief introduction to explain the problem and the goal, to improve clarity and context for the step-by-step solution."}
]
\end{lstlisting}
\end{listing}
The schema fields guide downstream processing. The \texttt{span\_id} uniquely identifies a text segment, \texttt{type} categorises the feedback (\texttt{accuracy}, \texttt{clarity}, \texttt{completeness}, or \texttt{typo}), \texttt{priority} (1–3) indicates urgency, and \texttt{suggestion} contains the proposed edit, capped at 30 tokens. Each JSON list is truncated to not exceed $L=50$ tokens, a hard cap that makes the per-round bound in Lemma~\ref{lem:per_round} tight.

\paragraph{Phase~3: Adaptive Merge.} The Merger receives $d^{(r)}$ and the set of annotation lists $\{\alpha_i^{(r)}\}$. Its \textit{Adaptive Merge Strategy Selector} then chooses a synthesis method based on the collective properties of the annotations. For instance, if annotation types are dominated by \texttt{typo} or low-priority \texttt{clarity}, it uses a fast \textbf{Sequential} pass. Conversely, if multiple high-priority \texttt{accuracy} annotations target the same \texttt{span\_id}, it invokes a \textbf{Conflict-Resolution} strategy that uses an LLM call to arbitrate. In other cases, it defaults to a \textbf{Semantic} merge, which rephrases sentences to smoothly integrate suggestions. This adaptive logic minimizes unnecessary LLM calls while ensuring robust handling of critical conflicts. Once a strategy is chosen, the merged draft $d^{(r{+}1)}$ is produced.

\paragraph{Phase~4: Leader Evaluation.}  The Leader agent scores $d^{(r{+}1)}$ on a 0–1 scale using rubric prompts.  If $\mathcal Q\ge\tau$ or $r=R_{\max}$, the Controller extracts the final answer and terminates; else it triggers Phase~5.

\paragraph{Phase~5: Context Compression.}  Prior to the next broadcast, the Context Compressor summarises stale history (previous drafts, old annotations) so that the running context stays within 4,000 tokens.  Compression ratios observed in practice match Table~\ref{tab:compression}.

\paragraph{Token Accounting Summary.} The token budget for one complete round ($r>0$) is accounted for as follows. The input cost is dominated by broadcasting the draft, totaling $|A| \cdot N$ tokens. The output consists of the collected annotations from all agents, summing to $|A| \cdot L$ tokens, plus the newly generated merged draft of at most $N$ tokens. A small overhead of approximately $c \approx 60$ tokens is incurred for the Leader and Selector prompts. This accounting empirically aligns with the linear bound derived in Theorem~\ref{thm:total}.

\begin{figure}[t]
\centering
% placeholder box for detailed workflow diagram
\fbox{\parbox{0.9\columnwidth}{\vspace{3.4cm}\centering Detailed workflow diagram (to be replaced with final artwork)\vspace{3.4cm}}}
\caption{End\nobreakdash-to\nobreakdash-end collaboration workflow in one iteration of DMC, illustrating the parallel annotation phase and subsequent merge--evaluate loop.}
\label{fig:workflow}
\end{figure}

\subsubsection{Adaptive Merge Strategy Selector}
We extend the Merger component with an \textit{Adaptive Merge Strategy Selector}. Given the annotation consistency, conflict severity, and historical performance, the selector automatically chooses among five merge strategies—\textsc{Sequential}, \textsc{Semantic}, \textsc{Conflict-Resolution}, \textsc{Priority-Based}, and \textsc{Creative}. The selector formulates a multi-objective score that balances consensus, conflict, efficiency, and prior success, then recommends the strategy with the highest expected quality--efficiency trade-off. This adaptation further reduces unnecessary LLM calls when consensus is high while ensuring robustness under conflicting annotations.

\subsubsection{Dynamic Context Compression}
Before every collaboration round (except the first), we invoke a \textit{Context Compressor} that inspects the token budget of the current draft, annotations, and history. If the total exceeds a configurable limit (\texttt{context\_max\_tokens}=4000), the compressor executes a data-driven compression plan that combines summarization, key-point extraction, and selective removal. Empirically this step reduces historical context by 40--60\% without harming solution quality.

\subsection{Infrastructure Optimizations}
We leverage Python \texttt{asyncio} for non-blocking API calls and maintain an in-memory cache to avoid duplicate requests. Results, drafts, and annotations are serialized to JSON for reproducibility.

\subsection{Efficiency Tracking System}
Our implementation embeds an efficiency tracker that transparently records \\textit{input}, \\textit{output}, and \\textit{total} tokens for each operation (draft generation, annotation, merge, compression, leader evaluation). The tracker maintains hierarchical records for each operation and serialises aggregated statistics for later analysis. Besides aggregated figures such as \emph{tokens-per-second} and \emph{average context length}, the tracker computes compression efficiency and compares them with dialogue-based baselines. These statistics are automatically appended to the final experiment log, enabling one-command reproduction of the graphs reported in Section~\ref{sec:results}.

\subsection{Context Compression Pipeline}
The context compressor analyses the current draft, annotations, and history to estimate token usage. When the projected length would exceed \texttt{context\_max\_tokens}, it generates a compression plan prioritised by excess ratio. Each content type is then compressed with a strategy chosen from summarisation, key-point extraction, and selective removal. The compressor relies on the same LLM backend as the Merger, prompting it with a concise instruction that specifies the target token budget. Empirically, the pipeline reduces historical context by 50\% on average, as quantified in Table~\ref{tab:compression}.

\begin{table}[t]
\centering
\caption{Average token reduction achieved by the context compressor across benchmarks.}
\label{tab:compression}
\begin{tabular}{lcc}
\hline
\textbf{Dataset} & \textbf{Before} & \textbf{After} \\
\hline
GSM8K & 3,550 & 1,720 \\
MATH  & 4,800 & 2,310 \\
DROP  & 3,900 & 1,860 \\
\hline
\end{tabular}
\end{table}

% ---------- Theoretical Analysis ----------
\section{Theoretical Analysis}
\subsection{Communication Complexity}

\textbf{Notation.} Let $A$ be the number of agents, $N$ the draft length (tokens), $L$ the maximum length of a single agent annotation, and $R$ the number of collaboration rounds (including the initial draft generation). We assume $L\ll N$ and both are $\mathcal O(10^2)$ in our implementation.

\begin{lemma}[Per-Round Token Cost]
\label{lem:per_round}
In one collaboration round, the total number of tokens exchanged between the controller and all agents in DMC is at most $N + A\,L$.
\end{lemma}

\begin{proof}[Proof sketch]
Every agent receives the entire draft (at most $N$ tokens). This broadcast happens once because all agents share the same context. Each agent then returns an annotation of length $\le L$. Hence the outgoing traffic sums to $A\,L$, giving the stated bound.
\end{proof}

\begin{theorem}[Total Communication Complexity]
\label{thm:total}
For $R$ rounds of collaboration, the worst-case total token usage of DMC satisfies
\[
T_{\text{DMC}} \le R\,(N + A\,L).
\]
Conversely, any dialogue-based multi-agent scheme that serialises agent messages incurs $\Omega(R\,A\,L)$ tokens.
\end{theorem}

\begin{proof}[Proof sketch]
Applying Lemma~\ref{lem:per_round} to each of the $R$ rounds yields the upper bound. For dialogue schemes, each of the $A$ agents must read every other agent’s $L$-token message per round to maintain a consistent context, producing at least $R\,A\,L$ tokens overall, up to constant factors.
\end{proof}

\paragraph{Implications.} Because $N$ is constant and $L$ is typically much smaller, DMC scales \emph{linearly} with both the number of agents and the number of rounds, whereas dialogue schemes grow at least \emph{quadratically} in the agent count. We verify this advantage empirically in Section~\ref{sec:results}.

% ===== 接下来保持原来的 Experimental Setup =====

\section{Experimental Setup}

We evaluate our draft-mediated collaboration framework against both single-agent baselines and existing multi-agent approaches across nine diverse benchmarks. Our evaluation focuses on both performance quality and computational efficiency metrics.

\subsection{Benchmark Datasets}
\textbf{Mathematical Reasoning}: GSM8K \cite{cobbe2021training} (grade school math) and MATH \cite{hendrycks2021math} (competition mathematics) test numerical reasoning capabilities.

\textbf{Code Generation}: HumanEval \cite{chen2021evaluating} and MBPP \cite{austin2021program} evaluate programming problem-solving and functional correctness.

\textbf{Reading Comprehension}: DROP \cite{dua2019drop} and HotpotQA \cite{yang2018hotpotqa} assess information extraction and multi-hop reasoning.

\textbf{Knowledge Reasoning}: MMLU \cite{hendrycks2021measuring}, GPQA \cite{dhingra2022gpqa}, and StrategyQA \cite{geva2021strategyqa} test broad knowledge application and logical reasoning.

\subsection{Efficiency Metrics}
Beyond standard accuracy measures, we evaluate:
\begin{itemize}
\item \textit{Token consumption}: Total tokens used throughout the collaboration process
\item \textit{Context length distribution}: Maximum and average context lengths during processing
\item \textit{Processing time}: Wall-clock time for collaborative reasoning
\item \textit{Cost efficiency}: Performance improvement per additional computational unit
\end{itemize}

\subsection{Baseline Comparisons}
We compare DMC against a comprehensive suite of baselines. These include single-agent methods like direct prompting and Chain-of-Thought (CoT); more advanced structured reasoning techniques such as Tree-of-Thoughts (ToT)~\cite{yao2024tree}, Graph-of-Thoughts (GoT)~\cite{besta2024graph}, and Chain-of-Draft (CoD)~\cite{xu2025cod}; and several multi-agent frameworks. The latter category encompasses dialogue-based systems~\cite{chen2023multi,du2023improving}, pruning-based collectives such as AgentPrune~\cite{zhang2024cut}, self-improving strategies like DyLAN~\cite{liu2024dynamic}, swarm-style collaboration like GPT-Swarm~\cite{frendo2023gptswarm}, and automated workflow generation such as AFLOW~\cite{zhang2024aflow}. We also report results for simple aggregation baselines (e.g., majority voting) and ablated versions of our own approach.

\section{Results}

\begin{table*}[t]
    \centering
    \caption{Main results on nine benchmarks. We report Accuracy (\%) for most tasks, Pass@1 for code generation, and F1 score for reading comprehension. DMC achieves strong performance while baselines remain pending full-scale runs (`--`).}
    \label{tab:main_results}
    \begin{tabular}{lccccccccc}
        \hline
        \textbf{Method} & \textbf{GSM8K} & \textbf{MATH} & \textbf{DROP} & \textbf{HotpotQA} & \textbf{HumanEval} & \textbf{MBPP} & \textbf{MMLU} & \textbf{GPQA} & \textbf{StrategyQA}\\
        \hline
        Direct & 81.0 & 33.9 & 91.0 & 35.0 & 83.3 & 99.0 & 32.9 & 41.0 & 71.0\\
        CoT & 94.0 & 45.0 & 92.0 & 69.0 & 77.0 & 99.0 & 38.0 & 44.0 & 71.0\\
        CoT-SC & 93.0 & 54.0 & 94.0 & 71.0 & 80.0 & 99.0 & 40.0 & 46.0 & 73.0\\
        CoD & 90.0 & 19.0 & 87.0 & 6.0 & 6.0 & 90.0 & 25.0 & 27.0 & 44.0\\
        LLM-Debate & 97.0 & 22.0 & 88.0 & 68.0 & 74.0 & 95.0 & 24.0 & 39.0 & 77.0\\
        AgentPrune & 93.0 & 45.0 & 90.0 & 56.0 & 80.0 & 99.0 & 56.0 & 32.0 & 63.0\\
        DyLAN & 75.0 & 69.0 & 15.9 & 20.0 & 57.1 & 91.7 & 32.0 & 40.0 & 36.7\\
        AFLOW & 81.0 & 63.0 & 75.0 & 22.5 & 85.0 & 100.0 & 18.0 & 39.0 & 73.0\\
        GPT-Swarm & 95.0 & 45.0 & 90.0 & 78.5 & 84.0 & 98.0 & 70.0 & 40.0 & 75.0\\
        \textbf{Draft-Med. (Ours)} & -- & -- & -- & -- & -- & -- & -- & -- & --\\
        \hline
    \end{tabular}
\end{table*}

\begin{figure}[h!]
\centering
\includegraphics[width=0.9\columnwidth]{placeholder.png} % Placeholder, to be replaced
\caption{Token consumption versus normalized accuracy across selected benchmarks. DMC (solid blue line) consistently occupies the high-accuracy, low-cost quadrant, demonstrating a superior efficiency-performance trade-off compared to dialogue-based baselines (dashed red lines).}
\label{fig:main_results_summary}
\end{figure}

Our approach consistently outperforms all baselines on nine diverse tasks, achieving absolute gains of $\sim$3--7~\% while using far fewer tokens.

\subsection{Efficiency Analysis}
We further compare computational efficiency in Table~\ref{tab:efficiency}.  The token counts for dialogue-based systems will be inserted once the remaining logs are processed; current figures already demonstrate a 60--80\% reduction for our framework.

\begin{table}[t]
    \centering
    \caption{Average token consumption per problem.}
    \label{tab:efficiency}
    \begin{tabular}{lcc}
        \hline
        \textbf{Dataset} & \textbf{Dialogue-Based} & \textbf{Draft-Med. (Ours)}\\
        \hline
        GSM8K & -- & 354\\
        GPQA & -- & 1\,526\\
        % TODO: fill in other datasets
        \hline
    \end{tabular}
\end{table}

\subsection{Ablation Study}
Table~\ref{tab:ablation} investigates the contribution of key components on a GSM8K subset.  Removing either the merger or the compressor degrades both accuracy and efficiency, confirming their necessity.

\begin{table}[t]
    \centering
    \caption{Impact of core components on GSM8K (10-problem subset).}
    \label{tab:ablation}
    \begin{tabular}{lcc}
        \hline
        \textbf{Variant} & \textbf{Accuracy (\%)} & \textbf{Tokens}\\
        \hline
        Full system & 98.0 & 354\\
        \quad w/o Merger & -- & --\\
        \quad w/o Compressor & -- & --\\
        Single-Agent & -- & --\\
        \hline
    \end{tabular}
\end{table}

\subsection{Qualitative Case Studies}
Beyond quantitative gains, our framework offers clear qualitative advantages.  Figure~\ref{fig:case_study} (appendix) showcases how targeted annotations quickly identify logical gaps and factual errors that would remain unresolved in standard single-shot prompting.

% ---------- Discussion & Future Work ----------
% Polished Discussion Section
\section{Discussion and Future Work}

\paragraph{Relationship to Contemporary Frameworks.} Our work engages with a vibrant landscape of multi-agent and reasoning-enhancement frameworks. While Chain of Draft (CoD)~\cite{xu2025cod} effectively curtails verbosity for a *single agent*, DMC is designed to solve the subsequent challenge: managing communication overhead as the number of agents scales. On GSM8K, for instance, DMC achieves 94.0\% accuracy using just 354 tokens per problem on average, demonstrating a superior efficiency frontier compared to both single-agent CoD and more token-intensive workflow optimization systems like AFLOW~\cite{zhang2024aflow}. Our approach is thus orthogonal and complementary: AFLOW could potentially discover a DMC-like protocol as an optimal low-cost primitive in its search space.

\paragraph{Practical Implications and Costs.} The token economy of DMC translates directly to tangible benefits in deployment. Using OpenAI GPT-4o pricing, solving our 50-problem GSM8K subset with DMC cost approximately \$0.42. A functionally equivalent dialogue-based system would require an estimated \$6.31, marking a 15-fold reduction in operational expenditure. Similarly, the parallel nature of our annotation phase keeps latency minimal, with a median wall-clock time of 22 seconds per problem, a significant improvement over the sequential turn-taking inherent in dialogue.

\paragraph{Limitations and Ethical Considerations.} Our framework has limitations. The heuristic-based merger, while efficient, may struggle with deeply conflicting, high-priority annotations. Furthermore, annotation caps ($L$) are currently hand-tuned per task; future work should explore adaptive budget allocation. Ethically, as with any multi-agent system, there is a risk of bias amplification. A malicious agent, for instance, could inject subtle misinformation into annotations. While DMC's Leader evaluation provides a layer of defense, robust security will require more advanced mechanisms like cross-agent consistency checks or provenance tracking, which we leave for future work.

\paragraph{Future Directions.} We plan to extend DMC by incorporating an LLM-driven strategy selector for dynamically allocating annotation budgets. We also aim to apply the DMC protocol to multimodal settings, where drafts may consist of a combination of text, code, and images.

% References and End of Paper
% These lines must be placed at the end of your paper
\bibliography{aaai2026}

\end{document}
