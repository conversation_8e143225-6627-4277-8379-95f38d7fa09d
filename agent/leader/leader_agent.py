#!/usr/bin/env python3
"""
Leader Agent for Multi-Agent Collaborative Draft Finalization

This module implements the Leader agent responsible for synthesizing the
final shared draft into a standardized format for evaluation.
"""

import asyncio
import json
import re
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass

from coordination.draft import SharedDraft
from utils.api import async_generate_completion
from prompt import DRAFT_PROMPTS, LeaderAgentPrompts, SYSTEM_PROMPT_MAPPINGS
from config import get_config


@dataclass
class FinalAnswer:
    """Final formatted answer ready for evaluation"""
    
    # Content
    final_answer: str               # The standardized final answer
    reasoning_summary: str          # Summary of the reasoning process
    confidence_score: float         # Overall confidence in the answer
    
    # Process metadata
    question_id: str
    question_type: str
    original_question: str
    
    # Collaboration metadata
    total_rounds: int
    participating_agents: List[str]
    final_draft_version: int
    synthesis_timestamp: str
    
    # Quality assessment
    leader_quality_assessment: float  # Leader's assessment of answer quality
    critic_final_score: Optional[float] = None
    meets_quality_threshold: bool = False


class LeaderAgent:
    """
    Leader agent responsible for synthesizing collaborative drafts into final answers.
    
    The Leader takes the shared draft and formats it into a standardized answer
    suitable for evaluation against the ground truth.
    """
    
    def __init__(self, agent_id: str = "leader"):
        self.agent_id = agent_id
        self.synthesis_count = 0
        self.synthesis_history = []
        
        # LLM configuration
        self.model_id = get_config("leader_model", "deepseek")
        
        # Quality thresholds for different question types (same as critic)
        self.quality_thresholds = {
            'gsm8k': 0.85,
            'math': 0.90,
            'mbpp': 0.80,
            'humaneval': 0.85,
            'drop': 0.75,
            'hotpotqa': 0.80,
            'strategyqa': 0.75,
            'gpqa': 0.85,
            'mmlu': 0.80,
            'default': 0.80
        }
    
    async def synthesize_final_answer(self, shared_draft: SharedDraftBook,
                                    critic_agent: Optional[EnhancedCriticAgent] = None) -> FinalAnswer:
        """
        Synthesize the shared draft into a final standardized answer.
        
        Args:
            shared_draft: The collaborative draft to synthesize
            critic_agent: Optional critic for final quality assessment
            
        Returns:
            Final answer object ready for evaluation
        """
        start_time = datetime.now()
        
        try:
            # Step 1: Assess if draft is ready for synthesis
            is_ready, readiness_issues = await self._assess_draft_readiness(shared_draft)
            
            if not is_ready:
                # Return a partial answer with issues noted
                return self._create_incomplete_answer(shared_draft, readiness_issues)
            
            # Step 2: Generate the final answer
            final_content = await self._generate_final_answer(shared_draft)
            
            # Step 3: Extract reasoning summary
            reasoning_summary = await self._extract_reasoning_summary(shared_draft)
            
            # Step 4: Assess confidence and quality
            confidence_score = self._calculate_confidence_score(shared_draft)
            quality_assessment = await self._assess_answer_quality(shared_draft, final_content)
            
            # Step 5: Get critic final evaluation if available
            critic_score = None
            if critic_agent:
                # Create a temporary draft with final content for evaluation
                temp_draft = SharedDraftBook(
                    shared_draft.question_id + "_final",
                    shared_draft.question_content,
                    shared_draft.question_type
                )
                temp_draft.current_content = final_content
                temp_draft.quality_score = quality_assessment
                
                critic_evaluation = await critic_agent.evaluate_draft(temp_draft, detailed=False)
                critic_score = critic_evaluation.overall_score
            
            # Step 6: Create final answer object
            final_answer = FinalAnswer(
                final_answer=final_content,
                reasoning_summary=reasoning_summary,
                confidence_score=confidence_score,
                question_id=shared_draft.question_id,
                question_type=shared_draft.question_type,
                original_question=shared_draft.question_content,
                total_rounds=shared_draft.total_rounds,
                participating_agents=shared_draft.active_agents.copy(),
                final_draft_version=shared_draft.version,
                synthesis_timestamp=datetime.now().isoformat(),
                leader_quality_assessment=quality_assessment,
                critic_final_score=critic_score,
                meets_quality_threshold=self._meets_threshold(quality_assessment, critic_score, shared_draft.question_type)
            )
            
            # Update synthesis tracking
            self.synthesis_count += 1
            self.synthesis_history.append({
                'question_id': shared_draft.question_id,
                'synthesis_time': (datetime.now() - start_time).total_seconds(),
                'final_quality': quality_assessment,
                'critic_score': critic_score,
                'meets_threshold': final_answer.meets_quality_threshold
            })
            
            return final_answer
            
        except Exception as e:
            # Return error answer
            return FinalAnswer(
                final_answer=f"Error in synthesis: {str(e)}",
                reasoning_summary="Synthesis process failed",
                confidence_score=0.0,
                question_id=shared_draft.question_id,
                question_type=shared_draft.question_type,
                original_question=shared_draft.question_content,
                total_rounds=shared_draft.total_rounds,
                participating_agents=shared_draft.active_agents.copy(),
                final_draft_version=shared_draft.version,
                synthesis_timestamp=datetime.now().isoformat(),
                leader_quality_assessment=0.0,
                meets_quality_threshold=False
            )
    
    async def _assess_draft_readiness(self, shared_draft: SharedDraftBook) -> Tuple[bool, List[str]]:
        """Assess if the draft is ready for final synthesis"""
        
        issues = []
        
        # Check if draft has content - but allow empty drafts to be processed!
        # The leader should be able to generate answers even from empty drafts
        if not shared_draft.current_content or shared_draft.current_content.strip() == "":
            print("ℹ️ Draft is empty, but leader will attempt to generate answer from question alone")
            # Don't add this as a blocking issue - just log it
        
        # Lower the quality threshold for readiness check
        min_quality = 0.0  # Allow any quality - leader should try to help
        if shared_draft.quality_score < min_quality:
            print(f"ℹ️ Quality score low: {shared_draft.quality_score:.2f}, but leader will attempt synthesis")
        
        # Check if there have been enough collaboration rounds - but be more lenient
        min_rounds = 0  # Allow even 0 rounds - leader should try to help
        if shared_draft.total_rounds < min_rounds:
            issues.append(f"Insufficient collaboration rounds: {shared_draft.total_rounds}")
        
        # For empty drafts, always try to generate an answer
        if not shared_draft.current_content or shared_draft.current_content.strip() == "":
            print("🚀 Empty draft detected - leader will generate answer directly from question")
            return True, []  # Always proceed with empty drafts
        
        # Quick content analysis for non-empty drafts
        content_assessment = await self._quick_content_assessment(shared_draft)
        if not content_assessment['has_answer']:
            print("ℹ️ No clear answer in draft, but leader will attempt to improve it")
        
        # Always allow processing - leader should be able to handle any situation
        return True, issues
    
    async def _quick_content_assessment(self, shared_draft: SharedDraftBook) -> Dict[str, Any]:
        """Quick assessment of draft content quality"""
        
        assessment_prompt = LeaderAgentPrompts.get_assessment_prompt(
            shared_draft.question_content, 
            shared_draft.question_type, 
            shared_draft.current_content
        )
        
        try:
            response = await async_generate_completion(
                agent_id=self.model_id,
                prompt=assessment_prompt,
                system_prompt=SYSTEM_PROMPT_MAPPINGS['leader_quality'],
                temperature=0.2
            )
            
            has_answer = "HAS_ANSWER: yes" in response.lower()
            coherent = "COHERENT: yes" in response.lower()
            major_issues = "MAJOR_ISSUES: yes" in response.lower()
            
            return {
                'has_answer': has_answer,
                'coherent': coherent,
                'major_issues': major_issues
            }
            
        except Exception:
            # Conservative fallback
            return {
                'has_answer': True,
                'coherent': True,
                'major_issues': False
            }
    
    async def _generate_final_answer(self, shared_draft: SharedDraftBook) -> str:
        """Generate the final standardized answer"""
        
        # Handle empty drafts by generating answer directly from question
        if not shared_draft.current_content or shared_draft.current_content.strip() == "":
            print("📝 Generating answer directly from question (empty draft)")
            
            direct_answer_prompt = LeaderAgentPrompts.get_direct_answer_prompt(
                shared_draft.question_content, 
                shared_draft.question_type
            )
            
            response = await async_generate_completion(
                agent_id=self.model_id,
                prompt=direct_answer_prompt,
                system_prompt=SYSTEM_PROMPT_MAPPINGS['leader_expert'],
                temperature=0.3
            )
            
            return self._clean_final_answer(response, shared_draft.question_type)
        
        # Original synthesis logic for non-empty drafts
        synthesis_prompt = LeaderAgentPrompts.get_synthesis_prompt(
            shared_draft.question_content, 
            shared_draft.question_type, 
            shared_draft.current_content
        )
        
        response = await async_generate_completion(
            agent_id=self.agent_id,
            prompt=synthesis_prompt,
            system_prompt=SYSTEM_PROMPT_MAPPINGS['leader_expert'],
            temperature=0.3
        )
        
        return self._clean_final_answer(response, shared_draft.question_type)
    
    async def _extract_reasoning_summary(self, shared_draft: SharedDraftBook) -> str:
        """Extract a concise summary of the reasoning process"""
        
        summary_prompt = f"""
Extract a concise summary of the reasoning process from this collaborative draft.

QUESTION: {shared_draft.question_content}
DRAFT: {shared_draft.current_content}

Focus on:
1. Key reasoning steps taken
2. Important insights or approaches used
3. How the conclusion was reached

Provide a 2-3 sentence summary of the reasoning process:
"""
        
        try:
            response = await async_generate_completion(
                agent_id=self.model_id,
                prompt=summary_prompt,
                system_prompt="You are summarizing reasoning processes concisely.",
                temperature=0.2
            )
            
            return response.strip()[:500]  # Limit length
            
        except Exception:
            return "Collaborative reasoning process applied to reach the final answer."
    
    def _calculate_confidence_score(self, shared_draft: SharedDraftBook) -> float:
        """Calculate overall confidence in the final answer"""
        
        # Base confidence from draft quality
        quality_confidence = shared_draft.quality_score
        
        # Boost confidence based on collaboration rounds
        rounds_confidence = min(1.0, shared_draft.total_rounds / 5.0)  # Max boost after 5 rounds
        
        # Boost confidence based on number of active agents
        agents_confidence = min(1.0, len(shared_draft.active_agents) / 3.0)  # Max boost with 3+ agents
        
        # Combined confidence (weighted average)
        overall_confidence = (
            quality_confidence * 0.6 +
            rounds_confidence * 0.2 +
            agents_confidence * 0.2
        )
        
        return min(1.0, overall_confidence)
    
    async def _assess_answer_quality(self, shared_draft: SharedDraftBook, final_answer: str) -> float:
        """Leader's independent assessment of answer quality"""
        
        quality_prompt = LeaderAgentPrompts.get_quality_assessment_prompt(
            shared_draft.question_content, 
            shared_draft.question_type, 
            final_answer
        )
        
        try:
            response = await async_generate_completion(
                agent_id=self.model_id,
                prompt=quality_prompt,
                system_prompt=SYSTEM_PROMPT_MAPPINGS['leader_quality'],
                temperature=0.1
            )
            
            # Extract score from response
            score_match = re.search(r'([0-9]*\.?[0-9]+)', response)
            if score_match:
                score = float(score_match.group(1))
                return min(1.0, max(0.0, score))
            
        except Exception:
            pass
        
        # Fallback: use draft quality as estimate
        return shared_draft.quality_score
    
    def _clean_final_answer(self, raw_answer: str, question_type: str) -> str:
        """Clean and format the final answer appropriately - optimized for HotpotQA"""
        
        cleaned = raw_answer.strip()
        
        # Special handling for HotpotQA - extract from ANSWER: prefix first
        if question_type == "hotpotqa":
            import re
            
            # Look for ANSWER: prefix (case insensitive)
            answer_pattern = r'(?:ANSWER:\s*|Answer:\s*)(.+?)(?:\n|$)'
            match = re.search(answer_pattern, cleaned, re.IGNORECASE | re.MULTILINE)
            
            if match:
                extracted = match.group(1).strip()
                # Remove any trailing punctuation, explanations, or extra text
                cleaned = extracted.split('.')[0].split(',')[0].split(';')[0].strip()
                # Remove quotes if present
                cleaned = cleaned.strip('"\'')
                return cleaned
        
        # Remove common prefixes for other question types
        prefixes_to_remove = [
            "ANSWER:",
            "FINAL ANSWER:",
            "Final Answer:",
            "Answer:",
            "The answer is:",
            "The final answer is:",
        ]
        
        for prefix in prefixes_to_remove:
            if cleaned.startswith(prefix):
                cleaned = cleaned[len(prefix):].strip()
        
        # Question-type specific formatting
        if question_type in ['gsm8k', 'math']:
            # For math problems, ensure we have a clear numerical answer
            cleaned = self._format_math_answer(cleaned)
        elif question_type in ['mbpp', 'humaneval']:
            # For code problems, ensure clean code format
            cleaned = self._format_code_answer(cleaned)
        elif question_type in ['strategyqa']:
            # For yes/no questions, ensure clear yes/no answer
            cleaned = self._format_yes_no_answer(cleaned)
        elif question_type == "hotpotqa":
            # For HotpotQA, ensure short phrase answers
            cleaned = self._format_hotpotqa_answer(cleaned)
        
        return cleaned
    
    def _format_math_answer(self, answer: str) -> str:
        """Format mathematical answers"""
        # Extract final numerical answer if present
        import re
        
        # Look for patterns like "= 42" or "Answer: 42"
        final_num_pattern = r'(?:=|answer:?|result:?)\s*([0-9,.-]+)'
        match = re.search(final_num_pattern, answer, re.IGNORECASE)
        
        if match:
            # Keep the full working but highlight the final answer
            return answer
        
        return answer
    
    def _format_code_answer(self, answer: str) -> str:
        """Format code answers"""
        # Ensure code is properly formatted
        if '```' not in answer and ('def ' in answer or 'function' in answer or 'class ' in answer):
            # Wrap in code block if not already
            return f"```python\n{answer}\n```"
        return answer
    
    def _format_yes_no_answer(self, answer: str) -> str:
        """Format yes/no answers"""
        answer_lower = answer.lower()
        if answer_lower.startswith('yes'):
            return answer
        elif answer_lower.startswith('no'):
            return answer
        elif 'yes' in answer_lower and 'no' not in answer_lower:
            return f"Yes. {answer}"
        elif 'no' in answer_lower and 'yes' not in answer_lower:
            return f"No. {answer}"
        
        return answer
    
    def _format_hotpotqa_answer(self, answer: str) -> str:
        """Format HotpotQA answers - ensure concise phrase answers"""
        import re
        
        # Remove common unwanted patterns
        unwanted_patterns = [
            r'\b(?:Based on|According to|The answer is|From the above|In conclusion|Therefore)\b.*?[,:.]',
            r'\b(?:Step \d+|First|Second|Third|Finally|Next)\b.*?[,:.]',
            r'\[.*?\]',  # Remove bracketed content
            r'\(.*?\)',  # Remove parenthetical content that might be explanatory
            r'Draft.*?:', # Remove draft indicators
            r'Note:.*', # Remove notes
        ]
        
        cleaned = answer.strip()
        
        # Apply unwanted pattern removal
        for pattern in unwanted_patterns:
            cleaned = re.sub(pattern, '', cleaned, flags=re.IGNORECASE)
        
        # Take only the first sentence/phrase if there are multiple
        # Split by sentence endings but keep the first meaningful part
        sentences = re.split(r'[.!?]+', cleaned)
        if sentences:
            # Get the first non-empty sentence
            first_sentence = next((s.strip() for s in sentences if s.strip()), cleaned)
            cleaned = first_sentence
        
        # Remove leading/trailing whitespace and common connectors
        cleaned = cleaned.strip()
        cleaned = re.sub(r'^(?:and |or |but |so |thus |hence )', '', cleaned, flags=re.IGNORECASE)
        
        # Limit length for conciseness (HotpotQA usually expects short answers)
        if len(cleaned) > 100:
            # Try to get meaningful shorter version
            words = cleaned.split()
            if len(words) > 15:
                cleaned = ' '.join(words[:15])
        
        return cleaned
    
    def _meets_threshold(self, leader_assessment: float, critic_score: Optional[float], 
                        question_type: str) -> bool:
        """Determine if the answer meets quality threshold"""
        
        threshold = self.quality_thresholds.get(question_type, 0.8)
        
        # Use critic score if available, otherwise leader assessment
        score_to_check = critic_score if critic_score is not None else leader_assessment
        
        return score_to_check >= threshold
    
    def _create_incomplete_answer(self, shared_draft: SharedDraftBook, 
                                 issues: List[str]) -> FinalAnswer:
        """Create answer for incomplete drafts"""
        
        incomplete_content = shared_draft.current_content or "No content available"
        if issues:
            incomplete_content += f"\n\n[Draft incomplete: {'; '.join(issues)}]"
        
        return FinalAnswer(
            final_answer=incomplete_content,
            reasoning_summary="Draft was incomplete",
            confidence_score=0.1,
            question_id=shared_draft.question_id,
            question_type=shared_draft.question_type,
            original_question=shared_draft.question_content,
            total_rounds=shared_draft.total_rounds,
            participating_agents=shared_draft.active_agents.copy(),
            final_draft_version=shared_draft.version,
            synthesis_timestamp=datetime.now().isoformat(),
            leader_quality_assessment=0.1,
            meets_quality_threshold=False
        )
    
    def get_synthesis_statistics(self) -> Dict[str, Any]:
        """Get statistics about synthesis performance"""
        
        if not self.synthesis_history:
            return {'total_syntheses': 0}
        
        avg_quality = sum(s['final_quality'] for s in self.synthesis_history) / len(self.synthesis_history)
        threshold_met_rate = sum(1 for s in self.synthesis_history if s['meets_threshold']) / len(self.synthesis_history)
        avg_synthesis_time = sum(s['synthesis_time'] for s in self.synthesis_history) / len(self.synthesis_history)
        
        return {
            'total_syntheses': len(self.synthesis_history),
            'average_final_quality': avg_quality,
            'threshold_met_rate': threshold_met_rate,
            'average_synthesis_time': avg_synthesis_time,
            'recent_performance': {
                'last_5_avg_quality': sum(s['final_quality'] for s in self.synthesis_history[-5:]) / min(5, len(self.synthesis_history)),
                'last_5_threshold_rate': sum(1 for s in self.synthesis_history[-5:] if s['meets_threshold']) / min(5, len(self.synthesis_history))
            }
        }