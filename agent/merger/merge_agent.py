#!/usr/bin/env python3
"""
LLM-Driven Merge Agent - 基于大模型的智能融合层
负责使用大语言模型创造性地组合多个达成共识的建议，处理冲突并维护整体一致性
"""

from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
import uuid
import json
import asyncio

from coordination.draft import SharedDraft, DraftAnnotation
from utils.api import async_generate_completion
from prompt import MergeAgentPrompts
from config import get_config

class MergeStrategy(Enum):
    """融合策略"""
    SEQUENTIAL_INTEGRATION = "sequential"       # 顺序融合
    SEMANTIC_SYNTHESIS = "semantic"            # 语义合成
    CONFLICT_RESOLUTION = "conflict_resolution" # 冲突解决
    PRIORITY_BASED = "priority_based"          # 优先级导向
    CREATIVE_COMBINATION = "creative"          # 创造性组合

class ConflictType(Enum):
    """冲突类型"""
    DIRECT_CONTRADICTION = "contradiction"     # 直接矛盾
    SEMANTIC_OVERLAP = "overlap"              # 语义重叠
    SCOPE_CONFLICT = "scope"                  # 范围冲突
    FORMAT_INCONSISTENCY = "format"           # 格式不一致
    LOGICAL_INCONSISTENCY = "logical"         # 逻辑不一致

@dataclass
class MergeOperation:
    """融合操作"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    operation_type: str = ""                   # 操作类型
    source_annotations: List[str] = field(default_factory=list)  # 源批注
    target_section: str = ""                   # 目标片段
    merged_content: str = ""                   # 融合后内容
    rationale: str = ""                        # 融合理由
    confidence: float = 0.8                    # 操作置信度
    timestamp: str = field(default_factory=lambda: datetime.now().isoformat())

@dataclass
class ConflictResolution:
    """冲突解决方案"""
    conflict_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    conflict_type: ConflictType = ConflictType.SEMANTIC_OVERLAP
    conflicting_annotations: List[str] = field(default_factory=list)
    resolution_strategy: str = ""              # 解决策略
    resolution_content: str = ""               # 解决方案内容
    confidence: float = 0.8                    # 解决方案置信度
    reasoning: str = ""                        # 解决推理

class LLMDrivenMergeAgent:
    """基于大模型的智能融合代理"""
    
    def __init__(self):
        self.merge_history: List[MergeOperation] = []
        self.conflict_resolutions: List[ConflictResolution] = []
        self.model_id = get_config("merge_agent_model", "qwen")
        self.max_tokens = get_config("merge_agent_max_tokens", 4096)
        self.temperature = get_config("merge_agent_temperature", 0.3)
        
        # LLM prompts for different merge strategies (from prompt)
        self.merge_prompts = {
            MergeStrategy.SEQUENTIAL_INTEGRATION: MergeAgentPrompts.get_sequential_merge_prompt(),
            MergeStrategy.SEMANTIC_SYNTHESIS: MergeAgentPrompts.get_semantic_synthesis_prompt(),
            MergeStrategy.CONFLICT_RESOLUTION: MergeAgentPrompts.get_conflict_resolution_prompt(),
            MergeStrategy.PRIORITY_BASED: MergeAgentPrompts.get_priority_based_prompt(),
            MergeStrategy.CREATIVE_COMBINATION: MergeAgentPrompts.get_creative_combination_prompt()
        }
    

    async def merge_consensus_annotations(self, shared_draft: SharedDraft,
                                        merge_strategy: MergeStrategy = MergeStrategy.SEMANTIC_SYNTHESIS) -> Dict[str, Any]:
        """使用LLM融合SharedDraft中达成共识的批注"""
        
        # 获取已达成共识的批注
        consensus_annotations = shared_draft.get_consensus_ready_annotations()
        
        if not consensus_annotations:
            return {"success": False, "error": "No consensus annotations to merge"}
            
        try:
            # 1. 预处理和分析批注
            annotation_analysis = await self._llm_analyze_shared_draft_annotations(
                consensus_annotations, shared_draft
            )
            
            # 2. 检测冲突和互补性
            conflicts = await self._llm_detect_annotation_conflicts(
                consensus_annotations, shared_draft
            )
            
            # 3. 识别互补性批注用于Creative策略
            complementary_groups = await self._identify_complementary_annotations(
                consensus_annotations, shared_draft
            )
            
            # 4. 选择合适的融合策略
            optimal_strategy = await self._select_optimal_merge_strategy(
                consensus_annotations, conflicts, complementary_groups, merge_strategy
            )
            
            # 5. 执行对应策略的LLM融合
            if optimal_strategy == MergeStrategy.CREATIVE_COMBINATION and complementary_groups:
                merge_result = await self._llm_execute_creative_combination_merge(
                    consensus_annotations, shared_draft, complementary_groups, annotation_analysis
                )
            else:
                merge_result = await self._llm_execute_shared_draft_merge(
                    consensus_annotations, shared_draft, optimal_strategy, 
                    annotation_analysis, conflicts
                )
            
            # 6. 验证融合结果
            validation_result = await self._llm_validate_shared_draft_merge(
                merge_result, shared_draft
            )
            
            # 7. 更新SharedDraft
            if validation_result["is_valid"]:
                shared_draft.update_content(
                    "merge_agent",
                    merge_result["merged_content"],
                    f"Merged {len(consensus_annotations)} consensus annotations"
                )
                
                # 标记已合并的批注
                for ann in consensus_annotations:
                    ann.status = "merged"
            
            # 8. 保存操作历史
            self._save_shared_draft_merge_operation(merge_result, consensus_annotations, shared_draft)
            
            return {
                "success": validation_result["is_valid"],
                "merged_draft": merge_result["merged_content"],
                "merge_operations": [merge_result],
                "conflicts_resolved": len(conflicts),
                "complementary_groups_merged": len(complementary_groups) if complementary_groups else 0,
                "strategy_used": optimal_strategy.value,
                "quality_score": validation_result["quality_score"],
                "merge_summary": merge_result.get("rationale", ""),
                "warnings": validation_result.get("warnings", []),
                "annotations_merged": len(consensus_annotations),
                "draft_version": shared_draft.version,
                "llm_analysis": {
                    "annotation_analysis": annotation_analysis,
                    "conflicts": conflicts,
                    "complementary_groups": complementary_groups,
                    "strategy_used": optimal_strategy.value
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"LLM merge failed: {str(e)}",
                "fallback_required": True
            }

    async def _llm_analyze_shared_draft_annotations(self, annotations: List[DraftAnnotation], 
                                                  shared_draft: SharedDraft) -> Dict[str, Any]:
        """使用LLM分析SharedDraft批注关系"""
        
        annotations_text = self._format_draft_annotations_for_llm(annotations)
        
        prompt = MergeAgentPrompts.get_annotation_analysis_prompt().format(
            current_draft=shared_draft.current_content[:2000] + "...",
            annotations_text=annotations_text
        )

        try:
            response = await async_generate_completion(
                self.model_id, prompt, max_tokens=1024, temperature=0.2
            )
            
            # 解析JSON响应
            if "```json" in response:
                json_str = response.split("```json")[1].split("```")[0].strip()
            else:
                json_str = response.strip()
                
            return json.loads(json_str)
            
        except Exception as e:
            # 返回默认分析结果
            return {
                "dependencies": [],
                "semantic_groups": [[ann.id for ann in annotations]],
                "suggested_order": [ann.id for ann in annotations],
                "complexity_assessment": "moderate"
            }

    async def _llm_detect_annotation_conflicts(self, annotations: List[DraftAnnotation], 
                                             shared_draft: SharedDraft) -> List[Dict[str, Any]]:
        """使用LLM检测批注冲突"""
        
        annotations_text = self._format_draft_annotations_for_llm(annotations)
        
        prompt = MergeAgentPrompts.get_conflict_detection_prompt().format(
            current_draft=shared_draft.current_content[:1500] + "...",
            annotations_text=annotations_text
        )

        try:
            response = await async_generate_completion(
                self.model_id, prompt, max_tokens=1024, temperature=0.2
            )
            
            if "```json" in response:
                json_str = response.split("```json")[1].split("```")[0].strip()
            else:
                json_str = response.strip()
                
            result = json.loads(json_str)
            return result.get("conflicts", [])
            
        except Exception as e:
            return []

    async def _llm_execute_shared_draft_merge(self, annotations: List[DraftAnnotation], 
                                            shared_draft: SharedDraft,
                                            strategy: MergeStrategy, analysis: Dict[str, Any],
                                            conflicts: List[Dict[str, Any]]) -> Dict[str, Any]:
        """使用LLM执行SharedDraft融合操作"""
        
        annotations_text = self._format_draft_annotations_for_llm(annotations)
        conflicts_text = json.dumps(conflicts, indent=2, ensure_ascii=False)
        analysis_text = json.dumps(analysis, indent=2, ensure_ascii=False)
        
        strategy_prompt = self.merge_prompts[strategy]
        
        full_prompt = MergeAgentPrompts.create_full_merge_prompt(
            strategy_prompt, shared_draft.current_content, annotations_text, analysis_text, conflicts_text
        )

        try:
            response = await async_generate_completion(
                self.model_id, full_prompt, 
                max_tokens=self.max_tokens, 
                temperature=self.temperature
            )
            
            # 解析响应
            if "```json" in response:
                json_str = response.split("```json")[1].split("```")[0].strip()
            else:
                # 尝试从响应中提取JSON
                start_idx = response.find('{')
                end_idx = response.rfind('}') + 1
                if start_idx != -1 and end_idx != 0:
                    json_str = response[start_idx:end_idx]
                else:
                    json_str = response.strip()
                    
            result = json.loads(json_str)
            
            # 确保包含必要字段
            if "merged_content" not in result:
                result["merged_content"] = shared_draft.current_content
            if "rationale" not in result:
                result["rationale"] = "LLM-driven shared draft merge completed"
            if "confidence" not in result:
                result["confidence"] = 0.8
                
            return result
            
        except Exception as e:
            # 返回原始内容作为后备
            return {
                "merged_content": shared_draft.current_content,
                "rationale": f"Merge failed, using original content. Error: {str(e)}",
                "confidence": 0.1,
                "error": str(e)
            }

    async def _llm_validate_shared_draft_merge(self, merge_result: Dict[str, Any], 
                                             shared_draft: SharedDraft) -> Dict[str, Any]:
        """使用LLM验证SharedDraft融合结果"""
        
        merged_content = merge_result.get("merged_content", "")
        
        prompt = MergeAgentPrompts.get_merge_validation_prompt().format(
            original_draft=shared_draft.current_content[:2000] + "...",
            merged_content=merged_content[:2000] + "...",
            rationale=merge_result.get('rationale', 'No rationale provided')
        )

        try:
            response = await async_generate_completion(
                self.model_id, prompt, max_tokens=1024, temperature=0.1
            )
            
            if "```json" in response:
                json_str = response.split("```json")[1].split("```")[0].strip()
            else:
                json_str = response.strip()
                
            result = json.loads(json_str)
            
            # 确保包含必要字段
            if "is_valid" not in result:
                result["is_valid"] = True
            if "quality_score" not in result:
                result["quality_score"] = 0.8
            if "warnings" not in result:
                result["warnings"] = []
                
            return result
            
        except Exception as e:
            # 返回默认验证结果
            return {
                "is_valid": True,
                "quality_score": 0.7,
                "warnings": [f"Validation failed: {str(e)}"]
            }

    def _format_draft_annotations_for_llm(self, annotations: List[DraftAnnotation]) -> str:
        """格式化SharedDraft批注用于LLM处理"""
        formatted = []
        for i, ann in enumerate(annotations, 1):
            formatted.append(f"""
批注 {i} (ID: {ann.id}):
- 代理: {ann.agent_id}
- 类型: {ann.annotation_type}
- 目标片段: {ann.target_text[:200]}...
- 建议内容: {ann.annotation_text}
- 优先级: {ann.priority}
- 共识度: {ann.consensus_score}
- 状态: {ann.status}
- 时间: {ann.timestamp}
""")
        return "\n".join(formatted)

    def _save_shared_draft_merge_operation(self, merge_result: Dict[str, Any], 
                                         annotations: List[DraftAnnotation], 
                                         shared_draft: SharedDraft):
        """保存SharedDraft融合操作到历史记录"""
        operation = MergeOperation(
            operation_type="shared_draft_llm_merge",
            source_annotations=[ann.id for ann in annotations],
            target_section="entire_document",
            merged_content=merge_result.get("merged_content", ""),
            rationale=merge_result.get("rationale", ""),
            confidence=merge_result.get("confidence", 0.8)
        )
        self.merge_history.append(operation)

    async def _llm_analyze_annotations(self, annotations: List[DraftAnnotation], current_draft: str) -> Dict[str, Any]:
        """使用LLM检测批注冲突"""
        
        annotations_text = self._format_annotations_for_llm(annotations)
        
        prompt = MergeAgentPrompts.get_conflict_detection_prompt().format(
            current_draft=current_draft[:1500] + "...",
            annotations_text=annotations_text
        )

        try:
            response = await async_generate_completion(
                self.model_id, prompt, max_tokens=1024, temperature=0.2
            )
            
            if "```json" in response:
                json_str = response.split("```json")[1].split("```")[0].strip()
            else:
                json_str = response.strip()
                
            result = json.loads(json_str)
            return result.get("conflicts", [])
            
        except Exception as e:
            return []

    async def _llm_execute_merge(self, annotations: List[DraftAnnotation], current_draft: str,
                               strategy: MergeStrategy, analysis: Dict[str, Any],
                               conflicts: List[Dict[str, Any]]) -> Dict[str, Any]:
        """使用LLM执行融合操作"""
        
        annotations_text = self._format_annotations_for_llm(annotations)
        conflicts_text = json.dumps(conflicts, indent=2, ensure_ascii=False)
        analysis_text = json.dumps(analysis, indent=2, ensure_ascii=False)
        
        strategy_prompt = self.merge_prompts[strategy]
        
        full_prompt = MergeAgentPrompts.create_full_merge_prompt(
            strategy_prompt, current_draft, annotations_text, analysis_text, conflicts_text
        )

        try:
            response = await async_generate_completion(
                self.model_id, full_prompt, 
                max_tokens=self.max_tokens, 
                temperature=self.temperature
            )
            
            # 解析响应
            if "```json" in response:
                json_str = response.split("```json")[1].split("```")[0].strip()
            else:
                # 尝试从响应中提取JSON
                start_idx = response.find('{')
                end_idx = response.rfind('}') + 1
                if start_idx != -1 and end_idx != 0:
                    json_str = response[start_idx:end_idx]
                else:
                    json_str = response.strip()
                    
            result = json.loads(json_str)
            
            # 确保包含必要字段
            if "merged_content" not in result:
                result["merged_content"] = current_draft
            if "rationale" not in result:
                result["rationale"] = "LLM-driven merge completed"
            if "confidence" not in result:
                result["confidence"] = 0.8
                
            return result
            
        except Exception as e:
            # 返回原始内容作为后备
            return {
                "merged_content": current_draft,
                "rationale": f"Merge failed, using original content. Error: {str(e)}",
                "confidence": 0.1,
                "error": str(e)
            }

    async def _llm_validate_merge(self, merge_result: Dict[str, Any], original_draft: str) -> Dict[str, Any]:
        """使用LLM验证融合结果"""
        
        merged_content = merge_result.get("merged_content", "")
        
        prompt = MergeAgentPrompts.get_merge_validation_prompt().format(
            original_draft=original_draft[:2000] + "...",
            merged_content=merged_content[:2000] + "...",
            rationale=merge_result.get('rationale', 'No rationale provided')
        )

        try:
            response = await async_generate_completion(
                self.model_id, prompt, max_tokens=1024, temperature=0.1
            )
            
            if "```json" in response:
                json_str = response.split("```json")[1].split("```")[0].strip()
            else:
                json_str = response.strip()
                
            result = json.loads(json_str)
            
            # 确保包含必要字段
            if "is_valid" not in result:
                result["is_valid"] = True
            if "quality_score" not in result:
                result["quality_score"] = 0.8
            if "warnings" not in result:
                result["warnings"] = []
                
            return result
            
        except Exception as e:
            # 返回默认验证结果
            return {
                "is_valid": True,
                "quality_score": 0.7,
                "warnings": [f"Validation failed: {str(e)}"]
            }

    def _format_annotations_for_llm(self, annotations: List[DraftAnnotation]) -> str:
        """格式化批注用于LLM处理"""
        formatted = []
        for i, ann in enumerate(annotations, 1):
            formatted.append(f"""
批注 {i} (ID: {ann.id}):
- 代理: {ann.agent_id}
- 类型: {ann.annotation_type}
- 目标片段: {ann.target_text[:200]}...
- 建议内容: {ann.annotation_text}
- 优先级: {ann.priority}
- 共识度: {ann.consensus_score}
- 状态: {ann.status}
- 时间: {ann.timestamp}
""")
        return "\n".join(formatted)

    async def _identify_complementary_annotations(self, annotations: List[DraftAnnotation], 
                                                shared_draft: SharedDraft) -> List[Dict[str, Any]]:
        """识别互补性批注组合用于创造性融合"""
        
        complementary_groups = []
        
        # 按target_text分组，寻找同一span的不同类型批注
        span_groups = {}
        for ann in annotations:
            span_key = ann.target_text[:50]  # 使用前50字符作为span标识
            if span_key not in span_groups:
                span_groups[span_key] = []
            span_groups[span_key].append(ann)
        
        # 分析每个span组，寻找互补性
        for span_key, span_annotations in span_groups.items():
            if len(span_annotations) >= 2:
                # 检查是否为互补性而非冲突性
                is_complementary = await self._check_complementary_relationship(span_annotations, shared_draft)
                if is_complementary:
                    complementary_groups.append({
                        'span_id': span_key,
                        'annotations': span_annotations,
                        'complementary_type': 'multi_aspect'  # 多维度互补
                    })
        
        return complementary_groups
    
    async def _check_complementary_relationship(self, annotations: List[DraftAnnotation], 
                                             shared_draft: SharedDraft) -> bool:
        """检查批注间是否为互补关系而非冲突关系"""
        
        if len(annotations) < 2:
            return False
        
        # 构建批注文本
        annotations_text = ""
        for i, ann in enumerate(annotations, 1):
            annotations_text += f"Annotation {i}: {ann.annotation_text}\n"
        
        prompt = f"""Analyze if these annotations are COMPLEMENTARY (can be combined to improve the text) or CONFLICTING (contradict each other).

TARGET TEXT: {annotations[0].target_text}
ANNOTATIONS:
{annotations_text}

Respond with either:
- COMPLEMENTARY: if annotations add different valuable aspects that can be combined
- CONFLICTING: if annotations contradict each other or are mutually exclusive

Classification:"""
        
        try:
            response = await async_generate_completion(
                self.model_id, prompt, max_tokens=50, temperature=0.1
            )
            
            return "COMPLEMENTARY" in response.upper()
            
        except Exception:
            # 保守估计：只有类型不同的批注才可能互补
            types = [ann.annotation_type for ann in annotations]
            return len(set(types)) > 1
    
    async def _select_optimal_merge_strategy(self, annotations: List[DraftAnnotation], 
                                           conflicts: List[Dict[str, Any]], 
                                           complementary_groups: List[Dict[str, Any]], 
                                           requested_strategy: MergeStrategy) -> MergeStrategy:
        """选择最优的融合策略"""
        
        # 如果有互补性组合，优先使用Creative策略
        if complementary_groups and len(complementary_groups) > 0:
            return MergeStrategy.CREATIVE_COMBINATION
        
        # 如果有冲突，使用冲突解决策略
        if conflicts and len(conflicts) > 0:
            return MergeStrategy.CONFLICT_RESOLUTION
        
        # 否则使用请求的策略
        return requested_strategy
    
    async def _llm_execute_creative_combination_merge(self, annotations: List[DraftAnnotation], 
                                                    shared_draft: SharedDraft,
                                                    complementary_groups: List[Dict[str, Any]], 
                                                    analysis: Dict[str, Any]) -> Dict[str, Any]:
        """执行创造性组合融合策略"""
        
        # 构建互补组合描述
        combinations_text = ""
        for i, group in enumerate(complementary_groups, 1):
            combinations_text += f"\nCOMBINATION {i} (span: {group['span_id'][:30]}...):\n"
            for j, ann in enumerate(group['annotations'], 1):
                combinations_text += f"  Aspect {j}: {ann.annotation_text}\n"
        
        prompt = f"""You are performing CREATIVE COMBINATION merge. Your task is to combine multiple complementary suggestions into improved, integrated content.

ORIGINAL DRAFT:
{shared_draft.current_content}

COMPLEMENTARY SUGGESTIONS TO COMBINE:
{combinations_text}

INSTRUCTIONS:
1. For each combination group, merge the suggestions into a single improved sentence/paragraph
2. Ensure the combined result captures the best aspects of all suggestions
3. Maintain coherence and readability
4. Generate the complete improved draft

Provide the result in JSON format:
{{
    "merged_content": "complete improved draft with all combinations integrated",
    "rationale": "explanation of how suggestions were creatively combined",
    "combinations_applied": [list of combination descriptions],
    "confidence": 0.0-1.0
}}"""
        
        try:
            response = await async_generate_completion(
                self.model_id, prompt, 
                max_tokens=self.max_tokens, 
                temperature=0.4  # 稍高创造性
            )
            
            # 解析响应
            if "```json" in response:
                json_str = response.split("```json")[1].split("```")[0].strip()
            else:
                start_idx = response.find('{')
                end_idx = response.rfind('}') + 1
                if start_idx != -1 and end_idx != 0:
                    json_str = response[start_idx:end_idx]
                else:
                    json_str = response.strip()
                    
            result = json.loads(json_str)
            
            # 确保包含必要字段
            if "merged_content" not in result:
                result["merged_content"] = shared_draft.current_content
            if "rationale" not in result:
                result["rationale"] = "Creative combination merge completed"
            if "confidence" not in result:
                result["confidence"] = 0.8
            if "combinations_applied" not in result:
                result["combinations_applied"] = [f"Combined {len(group['annotations'])} aspects" for group in complementary_groups]
                
            return result
            
        except Exception as e:
            # 返回原始内容作为后备
            return {
                "merged_content": shared_draft.current_content,
                "rationale": f"Creative combination failed, using original content. Error: {str(e)}",
                "confidence": 0.1,
                "combinations_applied": [],
                "error": str(e)
            }

    def _save_merge_operation(self, merge_result: Dict[str, Any], annotations: List[DraftAnnotation]):
        """保存融合操作到历史记录"""
        operation = MergeOperation(
            operation_type="llm_driven_merge",
            source_annotations=[ann.id for ann in annotations],
            target_section="entire_document",
            merged_content=merge_result.get("merged_content", ""),
            rationale=merge_result.get("rationale", ""),
            confidence=merge_result.get("confidence", 0.8)
        )
        self.merge_history.append(operation)

    def get_merge_stats(self) -> Dict[str, Any]:
        """获取融合统计信息"""
        if not self.merge_history:
            return {"total_merges": 0, "average_confidence": 0.0}
            
        total_merges = len(self.merge_history)
        avg_confidence = sum(op.confidence for op in self.merge_history) / total_merges
        
        return {
            "total_merges": total_merges,
            "average_confidence": avg_confidence,
            "model_used": self.model_id,
            "recent_operations": [
                {
                    "id": op.id,
                    "type": op.operation_type,
                    "confidence": op.confidence,
                    "timestamp": op.timestamp
                } for op in self.merge_history[-5:]
            ]
        }

# For backward compatibility
IntelligentMergeAgent = LLMDrivenMergeAgent