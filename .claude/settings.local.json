{"permissions": {"allow": ["Bash(scripts/run_all.sh:*)", "Bash(scripts/evaluate_sample.sh:*)", "Bash(./run_all.sh:*)", "<PERSON><PERSON>(timeout:*)", "Bash(find:*)", "<PERSON><PERSON>(python evaluate.py:*)", "Bash(python test_hotpotqa_improvements.py:*)", "<PERSON><PERSON>(python test:*)", "<PERSON><PERSON>(python:*)", "Bash(./run_sample.sh:*)", "<PERSON><PERSON>(chmod:*)", "Bash(./scripts/run_all.sh:*)", "Bash(bash:*)", "Bash(./scripts/evaluate_all.sh:*)", "Bash(rm:*)", "mcp__ide__executeCode", "Bash(ls:*)", "Bash(grep:*)", "Bash(cp:*)", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(true)", "Bash(tree:*)", "Bash(test -f /Users/<USER>/Desktop/LLM_MAS/main.py)", "Bash(/opt/anaconda3/envs/MAS/bin/python:*)", "<PERSON><PERSON>(source:*)", "Bash(./scripts/evaluate_baselines.sh:*)"], "deny": []}}