#!/usr/bin/env python3
"""
自适应合并策略选择器
根据批注一致度和冲突程度自动选择最优的合并策略
"""

from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
import json
import asyncio
import numpy as np

from agent.merger.merge_agent import MergeStrategy, ConflictType, LLMDrivenMergeAgent
from coordination.draft import SharedDraft, DraftAnnotation
from utils.api import async_generate_completion
from utils.token_utils import TokenCounter

class AnnotationConsistency(Enum):
    """批注一致性级别"""
    HIGH_CONSENSUS = "high_consensus"      # 高度一致 (>0.8)
    MODERATE_CONSENSUS = "moderate_consensus"  # 中等一致 (0.5-0.8)
    LOW_CONSENSUS = "low_consensus"        # 低一致性 (0.3-0.5)
    CONFLICTING = "conflicting"           # 冲突 (<0.3)

class ConflictSeverity(Enum):
    """冲突严重程度"""
    NO_CONFLICT = "no_conflict"           # 无冲突
    MINOR_CONFLICT = "minor_conflict"     # 轻微冲突
    MODERATE_CONFLICT = "moderate_conflict"  # 中等冲突
    MAJOR_CONFLICT = "major_conflict"     # 严重冲突

@dataclass
class AnnotationAnalysis:
    """批注分析结果"""
    total_annotations: int = 0
    consistency_level: AnnotationConsistency = AnnotationConsistency.MODERATE_CONSENSUS
    conflict_severity: ConflictSeverity = ConflictSeverity.NO_CONFLICT
    consensus_score: float = 0.0
    conflict_count: int = 0
    annotation_types: Dict[str, int] = field(default_factory=dict)
    agent_agreement_matrix: Dict[str, Dict[str, float]] = field(default_factory=dict)
    semantic_similarity: float = 0.0
    priority_distribution: Dict[int, int] = field(default_factory=dict)

@dataclass
class StrategyRecommendation:
    """策略推荐结果"""
    recommended_strategy: MergeStrategy = MergeStrategy.SEMANTIC_SYNTHESIS
    confidence: float = 0.8
    reasoning: str = ""
    alternative_strategies: List[MergeStrategy] = field(default_factory=list)
    expected_efficiency: float = 0.8
    risk_factors: List[str] = field(default_factory=list)

class AdaptiveMergeStrategySelector:
    """自适应合并策略选择器"""
    
    def __init__(self, analysis_agent: str = "openai"):
        self.analysis_agent = analysis_agent
        self.strategy_history: List[Dict[str, Any]] = []
        self.performance_metrics: Dict[MergeStrategy, Dict[str, float]] = {}
        
        # 策略选择规则权重
        self.strategy_weights = {
            "consistency_weight": 0.4,
            "conflict_weight": 0.3,
            "efficiency_weight": 0.2,
            "historical_performance_weight": 0.1
        }
    
    async def analyze_annotations(self, annotations: List[DraftAnnotation], 
                                draft: SharedDraft) -> AnnotationAnalysis:
        """分析批注的一致性和冲突程度"""
        
        if not annotations:
            return AnnotationAnalysis()
        
        analysis = AnnotationAnalysis()
        analysis.total_annotations = len(annotations)
        
        # 1. 计算基本统计
        analysis.annotation_types = {}
        analysis.priority_distribution = {}
        
        for ann in annotations:
            # 统计类型分布
            ann_type = ann.annotation_type
            analysis.annotation_types[ann_type] = analysis.annotation_types.get(ann_type, 0) + 1
            
            # 统计优先级分布
            priority = ann.priority
            analysis.priority_distribution[priority] = analysis.priority_distribution.get(priority, 0) + 1
        
        # 2. 计算共识分数
        consensus_scores = [ann.consensus_score for ann in annotations if hasattr(ann, 'consensus_score')]
        if consensus_scores:
            analysis.consensus_score = sum(consensus_scores) / len(consensus_scores)
        else:
            analysis.consensus_score = 0.5  # 默认中等共识
        
        # 3. 确定一致性级别
        if analysis.consensus_score > 0.8:
            analysis.consistency_level = AnnotationConsistency.HIGH_CONSENSUS
        elif analysis.consensus_score > 0.5:
            analysis.consistency_level = AnnotationConsistency.MODERATE_CONSENSUS
        elif analysis.consensus_score > 0.3:
            analysis.consistency_level = AnnotationConsistency.LOW_CONSENSUS
        else:
            analysis.consistency_level = AnnotationConsistency.CONFLICTING
        
        # 4. 检测冲突
        conflicts = await self._detect_semantic_conflicts(annotations, draft)
        analysis.conflict_count = len(conflicts)
        
        # 5. 确定冲突严重程度
        if analysis.conflict_count == 0:
            analysis.conflict_severity = ConflictSeverity.NO_CONFLICT
        elif analysis.conflict_count <= 2:
            analysis.conflict_severity = ConflictSeverity.MINOR_CONFLICT
        elif analysis.conflict_count <= 5:
            analysis.conflict_severity = ConflictSeverity.MODERATE_CONFLICT
        else:
            analysis.conflict_severity = ConflictSeverity.MAJOR_CONFLICT
        
        # 6. 计算语义相似度
        analysis.semantic_similarity = await self._calculate_semantic_similarity(annotations)
        
        # 7. 构建智能体同意度矩阵
        analysis.agent_agreement_matrix = self._build_agreement_matrix(annotations)
        
        return analysis
    
    async def recommend_strategy(self, analysis: AnnotationAnalysis, 
                               draft: SharedDraft) -> StrategyRecommendation:
        """基于分析结果推荐最优合并策略"""
        
        recommendation = StrategyRecommendation()
        
        # 策略评分系统
        strategy_scores = {
            MergeStrategy.SEQUENTIAL_INTEGRATION: 0.0,
            MergeStrategy.SEMANTIC_SYNTHESIS: 0.0,
            MergeStrategy.CONFLICT_RESOLUTION: 0.0,
            MergeStrategy.PRIORITY_BASED: 0.0,
            MergeStrategy.CREATIVE_COMBINATION: 0.0
        }
        
        # 1. 基于一致性级别评分
        consistency_scores = self._score_by_consistency(analysis.consistency_level)
        for strategy, score in consistency_scores.items():
            strategy_scores[strategy] += score * self.strategy_weights["consistency_weight"]
        
        # 2. 基于冲突严重程度评分
        conflict_scores = self._score_by_conflict_severity(analysis.conflict_severity)
        for strategy, score in conflict_scores.items():
            strategy_scores[strategy] += score * self.strategy_weights["conflict_weight"]
        
        # 3. 基于效率考虑评分
        efficiency_scores = self._score_by_efficiency(analysis)
        for strategy, score in efficiency_scores.items():
            strategy_scores[strategy] += score * self.strategy_weights["efficiency_weight"]
        
        # 4. 基于历史性能评分
        historical_scores = self._score_by_historical_performance()
        for strategy, score in historical_scores.items():
            strategy_scores[strategy] += score * self.strategy_weights["historical_performance_weight"]
        
        # 5. 选择最高分策略
        best_strategy = max(strategy_scores.items(), key=lambda x: x[1])
        recommendation.recommended_strategy = best_strategy[0]
        recommendation.confidence = min(0.95, best_strategy[1])
        
        # 6. 生成推理说明
        recommendation.reasoning = self._generate_reasoning(analysis, strategy_scores)
        
        # 7. 提供备选策略
        sorted_strategies = sorted(strategy_scores.items(), key=lambda x: x[1], reverse=True)
        recommendation.alternative_strategies = [s[0] for s in sorted_strategies[1:3]]
        
        # 8. 评估预期效率
        recommendation.expected_efficiency = self._estimate_efficiency(
            recommendation.recommended_strategy, analysis
        )
        
        # 9. 识别风险因素
        recommendation.risk_factors = self._identify_risk_factors(analysis, recommendation.recommended_strategy)
        
        return recommendation
    
    def _score_by_consistency(self, consistency: AnnotationConsistency) -> Dict[MergeStrategy, float]:
        """基于一致性级别为策略评分"""
        scores = {
            MergeStrategy.SEQUENTIAL_INTEGRATION: 0.5,
            MergeStrategy.SEMANTIC_SYNTHESIS: 0.5,
            MergeStrategy.CONFLICT_RESOLUTION: 0.5,
            MergeStrategy.PRIORITY_BASED: 0.5,
            MergeStrategy.CREATIVE_COMBINATION: 0.5
        }
        
        if consistency == AnnotationConsistency.HIGH_CONSENSUS:
            scores[MergeStrategy.SEQUENTIAL_INTEGRATION] = 0.9
            scores[MergeStrategy.SEMANTIC_SYNTHESIS] = 0.8
            scores[MergeStrategy.CREATIVE_COMBINATION] = 0.7
        elif consistency == AnnotationConsistency.MODERATE_CONSENSUS:
            scores[MergeStrategy.SEMANTIC_SYNTHESIS] = 0.9
            scores[MergeStrategy.PRIORITY_BASED] = 0.7
            scores[MergeStrategy.SEQUENTIAL_INTEGRATION] = 0.6
        elif consistency == AnnotationConsistency.LOW_CONSENSUS:
            scores[MergeStrategy.PRIORITY_BASED] = 0.9
            scores[MergeStrategy.CONFLICT_RESOLUTION] = 0.8
            scores[MergeStrategy.SEMANTIC_SYNTHESIS] = 0.6
        else:  # CONFLICTING
            scores[MergeStrategy.CONFLICT_RESOLUTION] = 0.9
            scores[MergeStrategy.PRIORITY_BASED] = 0.7
            scores[MergeStrategy.SEQUENTIAL_INTEGRATION] = 0.3
        
        return scores
    
    def _score_by_conflict_severity(self, severity: ConflictSeverity) -> Dict[MergeStrategy, float]:
        """基于冲突严重程度为策略评分"""
        scores = {
            MergeStrategy.SEQUENTIAL_INTEGRATION: 0.5,
            MergeStrategy.SEMANTIC_SYNTHESIS: 0.5,
            MergeStrategy.CONFLICT_RESOLUTION: 0.5,
            MergeStrategy.PRIORITY_BASED: 0.5,
            MergeStrategy.CREATIVE_COMBINATION: 0.5
        }
        
        if severity == ConflictSeverity.NO_CONFLICT:
            scores[MergeStrategy.SEQUENTIAL_INTEGRATION] = 0.9
            scores[MergeStrategy.SEMANTIC_SYNTHESIS] = 0.8
            scores[MergeStrategy.CREATIVE_COMBINATION] = 0.8
        elif severity == ConflictSeverity.MINOR_CONFLICT:
            scores[MergeStrategy.SEMANTIC_SYNTHESIS] = 0.9
            scores[MergeStrategy.PRIORITY_BASED] = 0.7
            scores[MergeStrategy.CONFLICT_RESOLUTION] = 0.6
        elif severity == ConflictSeverity.MODERATE_CONFLICT:
            scores[MergeStrategy.CONFLICT_RESOLUTION] = 0.9
            scores[MergeStrategy.PRIORITY_BASED] = 0.8
            scores[MergeStrategy.SEMANTIC_SYNTHESIS] = 0.5
        else:  # MAJOR_CONFLICT
            scores[MergeStrategy.CONFLICT_RESOLUTION] = 0.95
            scores[MergeStrategy.PRIORITY_BASED] = 0.6
            scores[MergeStrategy.SEQUENTIAL_INTEGRATION] = 0.3
        
        return scores
    
    def _score_by_efficiency(self, analysis: AnnotationAnalysis) -> Dict[MergeStrategy, float]:
        """基于效率考虑为策略评分"""
        scores = {
            MergeStrategy.SEQUENTIAL_INTEGRATION: 0.8,  # 最快
            MergeStrategy.SEMANTIC_SYNTHESIS: 0.6,      # 中等
            MergeStrategy.CONFLICT_RESOLUTION: 0.4,     # 较慢
            MergeStrategy.PRIORITY_BASED: 0.7,          # 较快
            MergeStrategy.CREATIVE_COMBINATION: 0.3     # 最慢
        }
        
        # 根据批注数量调整效率评分
        if analysis.total_annotations > 10:
            # 批注多时，优先选择高效策略
            scores[MergeStrategy.SEQUENTIAL_INTEGRATION] += 0.1
            scores[MergeStrategy.PRIORITY_BASED] += 0.1
            scores[MergeStrategy.CREATIVE_COMBINATION] -= 0.1
        
        return scores
    
    def _score_by_historical_performance(self) -> Dict[MergeStrategy, float]:
        """基于历史性能为策略评分"""
        # 默认评分
        scores = {
            MergeStrategy.SEQUENTIAL_INTEGRATION: 0.5,
            MergeStrategy.SEMANTIC_SYNTHESIS: 0.5,
            MergeStrategy.CONFLICT_RESOLUTION: 0.5,
            MergeStrategy.PRIORITY_BASED: 0.5,
            MergeStrategy.CREATIVE_COMBINATION: 0.5
        }
        
        # 基于历史性能调整
        for strategy, metrics in self.performance_metrics.items():
            if metrics.get("success_rate", 0) > 0.8:
                scores[strategy] += 0.2
            elif metrics.get("success_rate", 0) < 0.5:
                scores[strategy] -= 0.2
        
        return scores

    async def _detect_semantic_conflicts(self, annotations: List[DraftAnnotation],
                                       draft: SharedDraft) -> List[Dict[str, Any]]:
        """检测批注间的语义冲突"""

        if len(annotations) < 2:
            return []

        conflicts = []

        # 简单的冲突检测：检查是否有相互矛盾的建议
        for i, ann1 in enumerate(annotations):
            for j, ann2 in enumerate(annotations[i+1:], i+1):
                # 检查是否针对相同的目标文本
                if ann1.target_text == ann2.target_text and ann1.annotation_text != ann2.annotation_text:
                    # 使用LLM判断是否冲突
                    is_conflict = await self._llm_detect_conflict(ann1, ann2)
                    if is_conflict:
                        conflicts.append({
                            "type": "semantic_conflict",
                            "annotation_1": ann1.id,
                            "annotation_2": ann2.id,
                            "severity": "moderate",
                            "description": f"Conflicting suggestions for: {ann1.target_text[:50]}..."
                        })

        return conflicts

    async def _llm_detect_conflict(self, ann1: DraftAnnotation, ann2: DraftAnnotation) -> bool:
        """使用LLM检测两个批注是否冲突"""

        prompt = f"""Analyze if these two annotations conflict with each other:

Annotation 1 (by {ann1.agent_id}):
Target: "{ann1.target_text}"
Suggestion: "{ann1.annotation_text}"
Type: {ann1.annotation_type}

Annotation 2 (by {ann2.agent_id}):
Target: "{ann2.target_text}"
Suggestion: "{ann2.annotation_text}"
Type: {ann2.annotation_type}

Do these annotations contradict each other or suggest incompatible changes?
Respond with only "YES" or "NO"."""

        try:
            response = await async_generate_completion(
                agent_id=self.analysis_agent,
                prompt=prompt,
                system_prompt="You are an expert at detecting conflicts between suggestions.",
                temperature=0.1,
                max_tokens=10,
                skip_cache=True
            )

            return "YES" in response.upper()

        except Exception:
            # 如果LLM调用失败，使用简单的启发式方法
            return self._heuristic_conflict_detection(ann1, ann2)

    def _heuristic_conflict_detection(self, ann1: DraftAnnotation, ann2: DraftAnnotation) -> bool:
        """启发式冲突检测方法"""

        # 如果是相同目标文本的不同建议，可能冲突
        if ann1.target_text == ann2.target_text:
            # 检查关键词冲突
            conflict_pairs = [
                ("add", "remove"), ("increase", "decrease"), ("yes", "no"),
                ("correct", "incorrect"), ("good", "bad"), ("should", "shouldn't")
            ]

            text1_lower = ann1.annotation_text.lower()
            text2_lower = ann2.annotation_text.lower()

            for word1, word2 in conflict_pairs:
                if word1 in text1_lower and word2 in text2_lower:
                    return True
                if word2 in text1_lower and word1 in text2_lower:
                    return True

        return False

    async def _calculate_semantic_similarity(self, annotations: List[DraftAnnotation]) -> float:
        """计算批注间的语义相似度"""

        if len(annotations) < 2:
            return 1.0

        # 简化的相似度计算：基于共同词汇
        all_texts = [ann.annotation_text for ann in annotations]

        # 计算词汇重叠度
        word_sets = [set(text.lower().split()) for text in all_texts]

        total_similarity = 0.0
        comparisons = 0

        for i in range(len(word_sets)):
            for j in range(i+1, len(word_sets)):
                intersection = len(word_sets[i] & word_sets[j])
                union = len(word_sets[i] | word_sets[j])
                similarity = intersection / union if union > 0 else 0.0
                total_similarity += similarity
                comparisons += 1

        return total_similarity / comparisons if comparisons > 0 else 0.0

    def _build_agreement_matrix(self, annotations: List[DraftAnnotation]) -> Dict[str, Dict[str, float]]:
        """构建智能体间的同意度矩阵"""

        agents = list(set(ann.agent_id for ann in annotations))
        matrix = {}

        for agent1 in agents:
            matrix[agent1] = {}
            for agent2 in agents:
                if agent1 == agent2:
                    matrix[agent1][agent2] = 1.0
                else:
                    # 计算两个智能体批注的相似度
                    ann1_texts = [ann.annotation_text for ann in annotations if ann.agent_id == agent1]
                    ann2_texts = [ann.annotation_text for ann in annotations if ann.agent_id == agent2]

                    if ann1_texts and ann2_texts:
                        # 简化的相似度计算
                        similarity = self._calculate_text_similarity(ann1_texts[0], ann2_texts[0])
                        matrix[agent1][agent2] = similarity
                    else:
                        matrix[agent1][agent2] = 0.5  # 默认中等相似度

        return matrix

    def _calculate_text_similarity(self, text1: str, text2: str) -> float:
        """计算两个文本的相似度"""
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())

        intersection = len(words1 & words2)
        union = len(words1 | words2)

        return intersection / union if union > 0 else 0.0

    def _generate_reasoning(self, analysis: AnnotationAnalysis,
                          strategy_scores: Dict[MergeStrategy, float]) -> str:
        """生成策略选择的推理说明"""

        reasoning_parts = []

        # 一致性分析
        consistency_desc = {
            AnnotationConsistency.HIGH_CONSENSUS: "high consensus among annotations",
            AnnotationConsistency.MODERATE_CONSENSUS: "moderate consensus among annotations",
            AnnotationConsistency.LOW_CONSENSUS: "low consensus among annotations",
            AnnotationConsistency.CONFLICTING: "conflicting annotations"
        }
        reasoning_parts.append(f"Detected {consistency_desc[analysis.consistency_level]} (score: {analysis.consensus_score:.2f})")

        # 冲突分析
        conflict_desc = {
            ConflictSeverity.NO_CONFLICT: "no conflicts",
            ConflictSeverity.MINOR_CONFLICT: "minor conflicts",
            ConflictSeverity.MODERATE_CONFLICT: "moderate conflicts",
            ConflictSeverity.MAJOR_CONFLICT: "major conflicts"
        }
        reasoning_parts.append(f"Found {conflict_desc[analysis.conflict_severity]} ({analysis.conflict_count} conflicts)")

        # 策略选择原因
        best_strategy = max(strategy_scores.items(), key=lambda x: x[1])[0]
        strategy_reasons = {
            MergeStrategy.SEQUENTIAL_INTEGRATION: "Sequential integration chosen for straightforward, non-conflicting annotations",
            MergeStrategy.SEMANTIC_SYNTHESIS: "Semantic synthesis chosen to intelligently combine related suggestions",
            MergeStrategy.CONFLICT_RESOLUTION: "Conflict resolution chosen to address contradictory annotations",
            MergeStrategy.PRIORITY_BASED: "Priority-based merging chosen to focus on high-importance suggestions",
            MergeStrategy.CREATIVE_COMBINATION: "Creative combination chosen to synthesize diverse perspectives"
        }
        reasoning_parts.append(strategy_reasons[best_strategy])

        return ". ".join(reasoning_parts) + "."

    def _estimate_efficiency(self, strategy: MergeStrategy, analysis: AnnotationAnalysis) -> float:
        """估算策略的执行效率"""

        base_efficiency = {
            MergeStrategy.SEQUENTIAL_INTEGRATION: 0.9,
            MergeStrategy.SEMANTIC_SYNTHESIS: 0.7,
            MergeStrategy.CONFLICT_RESOLUTION: 0.5,
            MergeStrategy.PRIORITY_BASED: 0.8,
            MergeStrategy.CREATIVE_COMBINATION: 0.4
        }

        efficiency = base_efficiency[strategy]

        # 根据批注数量调整
        if analysis.total_annotations > 10:
            efficiency *= 0.9
        elif analysis.total_annotations < 3:
            efficiency *= 1.1

        # 根据冲突数量调整
        if analysis.conflict_count > 5:
            efficiency *= 0.8
        elif analysis.conflict_count == 0:
            efficiency *= 1.1

        return min(0.95, efficiency)

    def _identify_risk_factors(self, analysis: AnnotationAnalysis,
                             strategy: MergeStrategy) -> List[str]:
        """识别策略执行的风险因素"""

        risks = []

        if analysis.conflict_count > 5:
            risks.append("High number of conflicts may lead to information loss")

        if analysis.consensus_score < 0.3:
            risks.append("Low consensus may result in inconsistent merge results")

        if analysis.total_annotations > 15:
            risks.append("Large number of annotations may cause processing delays")

        if strategy == MergeStrategy.CREATIVE_COMBINATION and analysis.semantic_similarity < 0.3:
            risks.append("Creative combination with low similarity may produce incoherent results")

        if strategy == MergeStrategy.CONFLICT_RESOLUTION and analysis.conflict_count == 0:
            risks.append("Conflict resolution strategy chosen despite no detected conflicts")

        return risks

    def record_strategy_performance(self, strategy: MergeStrategy, success: bool,
                                  execution_time: float, quality_score: float):
        """记录策略执行性能"""

        if strategy not in self.performance_metrics:
            self.performance_metrics[strategy] = {
                "total_uses": 0,
                "successes": 0,
                "total_time": 0.0,
                "total_quality": 0.0
            }

        metrics = self.performance_metrics[strategy]
        metrics["total_uses"] += 1
        if success:
            metrics["successes"] += 1
        metrics["total_time"] += execution_time
        metrics["total_quality"] += quality_score

        # 计算平均值
        metrics["success_rate"] = metrics["successes"] / metrics["total_uses"]
        metrics["average_time"] = metrics["total_time"] / metrics["total_uses"]
        metrics["average_quality"] = metrics["total_quality"] / metrics["total_uses"]

        # 记录到历史
        self.strategy_history.append({
            "strategy": strategy.value,
            "success": success,
            "execution_time": execution_time,
            "quality_score": quality_score,
            "timestamp": datetime.now().isoformat()
        })

    def get_strategy_statistics(self) -> Dict[str, Any]:
        """获取策略使用统计"""

        return {
            "total_decisions": len(self.strategy_history),
            "performance_by_strategy": {
                strategy.value: metrics for strategy, metrics in self.performance_metrics.items()
            },
            "recent_decisions": self.strategy_history[-10:],
            "strategy_weights": self.strategy_weights
        }

    def get_merge_metadata(self, analysis: AnnotationAnalysis,
                          recommendation: StrategyRecommendation) -> Dict[str, Any]:
        """获取合并元数据供后续分析"""

        return {
            "analysis": {
                "total_annotations": analysis.total_annotations,
                "consistency_level": analysis.consistency_level.value,
                "conflict_severity": analysis.conflict_severity.value,
                "consensus_score": analysis.consensus_score,
                "conflict_count": analysis.conflict_count,
                "semantic_similarity": analysis.semantic_similarity,
                "annotation_types": analysis.annotation_types,
                "priority_distribution": analysis.priority_distribution,
                "agent_agreement_matrix": analysis.agent_agreement_matrix
            },
            "recommendation": {
                "strategy": recommendation.recommended_strategy.value,
                "confidence": recommendation.confidence,
                "reasoning": recommendation.reasoning,
                "alternative_strategies": [s.value for s in recommendation.alternative_strategies],
                "expected_efficiency": recommendation.expected_efficiency,
                "risk_factors": recommendation.risk_factors
            },
            "decision_context": {
                "strategy_weights": self.strategy_weights,
                "historical_performance_available": len(self.performance_metrics) > 0,
                "timestamp": datetime.now().isoformat()
            }
        }
