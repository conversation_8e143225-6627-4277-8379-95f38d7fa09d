#!/usr/bin/env python3
"""
重新设计的简化协作控制器
实现正确的流程: Worker协作讨论→批注→Merger融合→Leader评估→反馈循环
专注于简洁高效的draft生成和高正确率
"""

from typing import Dict, List, Optional, Any
from datetime import datetime
import asyncio
import json
import uuid
import os
from pathlib import Path

from utils.api import async_generate_completion
from utils.token_utils import AnnotationLengthController, TokenCounter
from utils.efficiency_tracker import EfficiencyTracker, OperationType
from coordination.merge_strategy import AdaptiveMergeStrategySelector
from coordination.context_compressor import ContextCompressor
from agent.merger.merge_agent import LLMDrivenMergeAgent, MergeStrategy
# Removed complex worker annotation system - using simple annotations
from prompt import (
    get_worker_draft_prompt,
    get_worker_collaboration_prompt,
    get_merger_prompt,
    get_leader_evaluation_prompt,
    get_system_prompt,
    get_expected_format,
    create_complete_worker_prompt
)
from config import get_config

class SimplifiedDraft:
    """真正简化的Draft类 - 轻量级数据容器"""

    def __init__(self, question: str, task_type: str, session_id: str):
        # 核心数据
        self.question = question
        self.task_type = task_type
        self.session_id = session_id
        self.content = ""
        self.annotations = []
        self.version = 1
        self.participants = []
        self.quality_score = 0.0
        self.created_at = datetime.now()

        # 简化的历史记录 - 只保存关键信息
        self.history = []

    def add_annotation(self, agent_id: str, annotation: str, annotation_type: str = "suggestion"):
        """添加批注 - 带可视化的版本"""

        # 简单的长度控制 - 直接截断超长注释
        max_tokens = 100
        original_length = len(annotation)
        if len(annotation) > max_tokens * 4:  # 粗略估算 1 token ≈ 4 chars
            annotation = annotation[:max_tokens * 4] + "..."

        self.annotations.append({
            "agent_id": agent_id,
            "text": annotation,
            "type": annotation_type,
            "timestamp": datetime.now().isoformat()
        })

        # 可视化注释添加
        self._show_annotation_added(agent_id, annotation, annotation_type, original_length)

    def _show_annotation_added(self, agent_id: str, annotation: str, annotation_type: str, original_length: int):
        """显示注释添加的可视化信息"""

        truncated = len(annotation) < original_length
        truncate_info = f" (truncated from {original_length})" if truncated else ""

        print(f"         📝 {agent_id} annotation ({annotation_type}){truncate_info}:")
        preview = annotation[:100] + "..." if len(annotation) > 100 else annotation
        print(f"         💬 \"{preview}\"")
        print()

    def add_annotation_sync(self, agent_id: str, annotation: str, annotation_type: str = "suggestion"):
        """添加批注（同步版本） - 简化版本"""
        # 直接调用简化的 add_annotation
        self.add_annotation(agent_id, annotation, annotation_type)
        
    def update_content(self, new_content: str, agent_id: str = "merger", phase: str = "update"):
        """更新draft内容 - 带可视化的版本"""

        # 保存历史记录（保存完整内容用于演进追踪）
        if self.content:
            self.history.append({
                "version": self.version,
                "agent_id": agent_id,
                "phase": phase,
                "timestamp": datetime.now().isoformat(),
                "content_length": len(self.content),
                "content_preview": self.content[:150] + "..." if len(self.content) > 150 else self.content,
                "full_content": self.content  # 保存完整内容用于对比
            })

        # 显示更新信息
        old_content = self.content
        self.content = new_content
        self.version += 1
        if agent_id not in self.participants:
            self.participants.append(agent_id)

        # 可视化更新过程
        self._show_draft_update(old_content, new_content, agent_id, phase)

    def _show_draft_update(self, old_content: str, new_content: str, agent_id: str, phase: str):
        """显示 draft 更新的可视化信息 - 增强版"""

        print(f"      📝 Draft Update by {agent_id} ({phase})")
        print(f"         Version: {self.version-1} → {self.version}")

        # 显示内容变化
        if not old_content:
            print(f"         📄 Initial content ({len(new_content)} chars):")
            preview = new_content[:200] + "..." if len(new_content) > 200 else new_content
            print(f"         💭 \"{preview}\"")
        else:
            old_len = len(old_content)
            new_len = len(new_content)
            change = new_len - old_len
            change_str = f"+{change}" if change > 0 else str(change)

            print(f"         📊 Content: {old_len} → {new_len} chars ({change_str})")

            # 显示具体变更内容
            if new_content != old_content:
                # 显示变更前后对比
                print(f"         📋 BEFORE: \"{old_content[:100]}{'...' if len(old_content) > 100 else ''}\"")
                print(f"         📋 AFTER:  \"{new_content[:100]}{'...' if len(new_content) > 100 else ''}\"")

                # 如果是大幅缩短（如 leader 生成最终答案），特别标注
                if change < -50:
                    print(f"         🎯 Significant reduction: Likely final answer extraction")
                elif change > 50:
                    print(f"         📈 Significant expansion: Content enhanced/merged")
            else:
                print(f"         ℹ️ Content unchanged")

        print()

            
    def save_final_draft(self, draft_dir: Path):
        """保存完整的draft演进历史"""
        try:
            # 构建完整的演进历史
            evolution_history = []

            # 添加初始状态
            if self.history:
                evolution_history.append({
                    "version": 1,
                    "phase": "initial",
                    "agent_id": "system",
                    "content": "",
                    "content_length": 0,
                    "timestamp": self.created_at.isoformat(),
                    "description": "Initial empty draft"
                })

            # 添加所有历史版本
            for hist in self.history:
                evolution_history.append({
                    "version": hist["version"],
                    "phase": hist["phase"],
                    "agent_id": hist["agent_id"],
                    "content": hist.get("content_preview", ""),
                    "content_length": hist["content_length"],
                    "timestamp": hist["timestamp"],
                    "description": f"{hist['agent_id']} {hist['phase']}"
                })

            # 添加最终版本
            evolution_history.append({
                "version": self.version,
                "phase": "final",
                "agent_id": "system",
                "content": self.content,
                "content_length": len(self.content),
                "timestamp": datetime.now().isoformat(),
                "description": "Final approved draft"
            })

            final_data = {
                "session_id": self.session_id,
                "question": self.question,
                "task_type": self.task_type,
                "final_content": self.content,
                "participants": self.participants,
                "annotations": self.annotations,
                "quality_score": self.quality_score,
                "completed_at": datetime.now().isoformat(),
                "evolution_history": evolution_history,
                "summary": {
                    "total_versions": len(evolution_history),
                    "total_annotations": len(self.annotations),
                    "collaboration_phases": list(set(h["phase"] for h in evolution_history))
                }
            }

            draft_dir.mkdir(parents=True, exist_ok=True)
            # 使用 session_id 作为文件名，避免文件夹嵌套
            summary_path = draft_dir / f"{self.session_id}_evolution.json"
            with open(summary_path, 'w', encoding='utf-8') as f:
                json.dump(final_data, f, indent=2, ensure_ascii=False)

            print(f"📁 Draft evolution saved to: {summary_path}")

        except Exception as e:
            print(f"Warning: Failed to save draft evolution: {e}")

    def get_compression_statistics(self) -> Dict[str, Any]:
        """获取注释压缩统计信息 - 简化版本"""
        return {
            "total_annotations": len(self.annotations),
            "total_versions": len(self.history) + 1,
            "participants": len(self.participants)
        }






            
    def get_summary(self) -> Dict[str, Any]:
        """获取draft摘要 - 简化版本"""
        return {
            "session_id": self.session_id,
            "question": self.question,
            "task_type": self.task_type,
            "content": self.content,
            "version": self.version,
            "participants": self.participants,
            "annotations_count": len(self.annotations),
            "quality_score": self.quality_score
        }

    def show_current_status(self):
        """显示当前 draft 状态"""
        print(f"      📋 Current Draft Status:")
        print(f"         Version: {self.version}")
        print(f"         Content length: {len(self.content)} chars")
        print(f"         Annotations: {len(self.annotations)}")
        print(f"         Participants: {', '.join(self.participants)}")
        if self.content:
            preview = self.content[:200] + "..." if len(self.content) > 200 else self.content
            print(f"         📄 Content: \"{preview}\"")
        print()

class SimplifiedCollaborativeController:
    """
    简化的协作控制器
    
    正确流程:
    1. Worker协作讨论 → 生成简洁draft
    2. Worker添加批注 → 标注关键点
    3. Merger融合 → 整合为完整draft
    4. Leader评估 → 判断正确性
    5. 反馈循环 → 如果错误，给出改进思路
    """
    
    def __init__(self):
        self.max_rounds = get_config("collaborative_max_rounds", 3)
        self.quality_threshold = get_config("collaborative_quality_threshold", 0.8)
        self.time_limit = get_config("collaborative_max_duration", 60)

        # 自适应合并组件
        self.strategy_selector = AdaptiveMergeStrategySelector()
        self.merge_agent = LLMDrivenMergeAgent()
        self.merge_statistics = []

        # 上下文压缩组件
        self.context_compressor = ContextCompressor()
        self.compression_statistics = []

        # 效率追踪组件
        self.efficiency_tracker = EfficiencyTracker()
        
    async def process_task(self, question: str, task_type: str, 
                          available_agents: List[str], problem_id: str = None) -> Dict[str, Any]:
        """处理协作任务的主入口"""
        
        start_time = datetime.now()
        session_id = problem_id or str(uuid.uuid4())
        
        print(f"🚀 Starting Simplified Collaborative Processing")
        print(f"   Session: {session_id}")
        print(f"   Task Type: {task_type}")
        print(f"   Agents: {available_agents}")
        print(f"   Max Rounds: {self.max_rounds}")
        
        try:
            # 开始效率追踪
            self.efficiency_tracker.start_session(session_id, task_type, problem_id or "")

            # 初始化draft
            draft = SimplifiedDraft(question, task_type, session_id)

            # 判断题目难度，决定轮数
            max_rounds = self._determine_rounds_needed(question, task_type)
            print(f"   📊 Estimated rounds needed: {max_rounds}")
            
            # 协作循环
            evaluation = {'quality_score': 0.5, 'decision': 'needs_minor_revision', 'feedback': 'No evaluation performed'}

            for round_num in range(1, max_rounds + 1):
                # 开始轮次追踪
                self.efficiency_tracker.start_operation(
                    OperationType.COLLABORATION_ROUND,
                    f"round_{round_num}",
                    round_num,
                    TokenCounter.count_tokens(draft.content)
                )
                print(f"\n🔄 Round {round_num}/{max_rounds}")

                # 检查时间限制
                if self._check_time_limit(start_time):
                    print("⏱️ Time limit reached, ending collaboration")
                    break

                # 阶段0: 上下文压缩（从第2轮开始）
                if round_num > 1:
                    draft = await self._context_compression_phase(draft)

                # 阶段1: Worker协作讨论
                draft = await self._worker_collaboration_phase(draft, available_agents)
                
                # 阶段2: Worker添加批注
                draft = await self._worker_annotation_phase(draft, available_agents)
                
                # 阶段3: Merger融合
                draft = await self._merger_phase(draft)
                
                # 阶段4: Leader评估和最终答案生成
                leader_result = await self._leader_evaluation_and_final_answer_phase(draft)
                evaluation = leader_result['evaluation']
                
                # 如果Leader生成了最终答案，使用它
                if leader_result.get('final_answer'):
                    draft.update_content(leader_result['final_answer'], "leader", "final_answer_generation")
                
                # 确保evaluation包含所有必需的键，兼容新旧格式
                if 'quality_score' not in evaluation:
                    evaluation['quality_score'] = evaluation.get('overall', evaluation.get('overall_score', 0.5))
                if 'decision' not in evaluation:
                    evaluation['decision'] = 'needs_minor_revision'
                if 'feedback' not in evaluation:
                    evaluation['feedback'] = 'No specific feedback provided'
                
                # 记录轮次完成
                self.efficiency_tracker.record_context_change(
                    TokenCounter.count_tokens(draft.content)
                )
                self.efficiency_tracker.finish_operation(
                    evaluation['quality_score'],
                    evaluation['decision'] != 'rejected'
                )

                # 阶段5: 判断是否需要继续 - 使用多维度分数和结构化反馈
                overall_score = evaluation.get('overall', evaluation.get('quality_score', 0.5))
                if evaluation['decision'] == 'approved':
                    print(f"   ✅ Draft approved! Overall Quality: {overall_score:.2f}")
                    break
                elif round_num < max_rounds:
                    print(f"   🔄 Continue to next round based on multi-dimensional feedback")
                    
                    # 添加结构化反馈作为Worker定向任务
                    await self._add_leader_feedback_as_worker_tasks(draft, evaluation)
                else:
                    print(f"   ⚠️ Max rounds reached. Using current draft.")
            
            # 完成效率追踪
            self.efficiency_tracker.finish_session(
                evaluation.get('quality_score', 0.0),
                evaluation.get('decision') != 'rejected'
            )

            # 生成最终结果 - 添加leader synthesis步骤
            total_time = (datetime.now() - start_time).total_seconds()

            # 保存最终draft - 统一到 drafts 文件夹
            draft_dir = Path(f"results/drafts/{draft.task_type}")
            draft.save_final_draft(draft_dir)

            # 使用Leader评估阶段生成的最终答案
            final_answer = evaluation.get('final_answer')
            if not final_answer:
                # 如果Leader没有生成最终答案，从draft中提取
                final_answer = self._extract_final_answer(draft.content, draft.task_type)

            result = self._create_final_result(draft, evaluation, total_time, session_id, final_answer)

            # 添加效率统计到结果
            efficiency_summary = self.efficiency_tracker.get_efficiency_summary(task_type)
            result['efficiency_metrics'] = efficiency_summary

            return result
            
        except Exception as e:
            print(f"❌ Collaboration failed: {e}")
            total_time = (datetime.now() - start_time).total_seconds()
            return {
                'success': False,
                'solution': f"Collaboration failed: {str(e)}",
                'total_processing_time': total_time,
                'mode': 'collaborative_error',
                'session_id': session_id
            }
    
    def _determine_rounds_needed(self, question: str, task_type: str) -> int:
        """基于题目难度判断需要的轮数"""

        # 简单题型，通常1轮就够
        simple_tasks = ['gsm8k', 'strategyqa']
        if task_type in simple_tasks:
            return 1

        # 编程题，可能需要2轮
        programming_tasks = ['mbpp', 'humaneval']
        if task_type in programming_tasks:
            return 2

        # 复杂推理题，可能需要2-3轮
        complex_tasks = ['math', 'hotpotqa', 'gpqa', 'drop', 'mmlu']
        if task_type in complex_tasks:
            # 简单判断：短问题1轮，长问题2轮
            return 1 if len(question) < 200 else 2

        return 1  # 默认1轮
    
    def _check_time_limit(self, start_time: datetime) -> bool:
        """检查是否超时"""
        elapsed = (datetime.now() - start_time).total_seconds()
        return elapsed > self.time_limit
    
    async def _worker_collaboration_phase(self, draft: SimplifiedDraft, 
                                        available_agents: List[str]) -> SimplifiedDraft:
        """阶段1: Worker协作讨论生成draft"""
        
        print("   📝 Phase 1: Worker Collaboration")
        
        # 获取之前的draft内容（如果有）
        existing_content = draft.content if draft.content else ""
        
        # 获取Leader的改进建议（如果有）
        leader_feedback = ""
        for ann in draft.annotations:
            if ann['agent_id'] == 'leader' and ann['type'] == 'improvement_guidance':
                leader_feedback = ann['text']
                break
        
        # 并行生成多个draft
        tasks = []
        for agent_id in available_agents:
            task = self._generate_worker_draft(
                agent_id, draft.question, draft.task_type, 
                existing_content, leader_feedback
            )
            tasks.append(task)
        
        # 等待所有Worker完成
        worker_drafts = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 选择最好的draft
        best_draft = self._select_best_draft(worker_drafts, available_agents)
        draft.update_content(best_draft['content'], best_draft['agent_id'], "worker_collaboration")

        print(f"      ✅ Best draft selected from {best_draft['agent_id']}")
        return draft
    
    async def _generate_worker_draft(self, agent_id: str, question: str, 
                                   task_type: str, existing_content: str = "", 
                                   leader_feedback: str = "") -> Dict[str, Any]:
        """单个Worker生成draft"""
        
        try:
            # 根据是否有现有内容选择prompt
            if existing_content and leader_feedback:
                # 改进现有draft
                prompt = get_worker_collaboration_prompt(
                    'improve',
                    question=question,
                    task_type=task_type,
                    current_draft=existing_content,
                    feedback=leader_feedback,
                    task_prompt=get_worker_draft_prompt(task_type)
                )
            else:
                # 生成新draft
                prompt = create_complete_worker_prompt(task_type, question)
            
            # 开始操作追踪
            self.efficiency_tracker.start_operation(
                OperationType.WORKER_DRAFT,
                agent_id,
                0,  # 初始轮次
                TokenCounter.count_tokens(prompt)
            )

            # 调用LLM生成draft
            system_prompt = get_system_prompt(task_type)
            result = await async_generate_completion(
                agent_id=agent_id,
                prompt=prompt,
                system_prompt=system_prompt,
                max_tokens=512,  # 保持简洁
                temperature=0.3
            )

            # 记录精确的Token使用
            self.efficiency_tracker.record_token_usage(
                prompt_text=prompt + system_prompt,
                response_text=result or ""
            )
            
            # 完成操作追踪
            self.efficiency_tracker.finish_operation(0.8, bool(result))

            return {
                'agent_id': agent_id,
                'content': result or "",
                'success': bool(result)
            }

        except Exception as e:
            print(f"      ❌ {agent_id} draft generation failed: {e}")
            self.efficiency_tracker.finish_operation(0.0, False, str(e))
            return {
                'agent_id': agent_id,
                'content': "",
                'success': False,
                'error': str(e)
            }
    
    def _select_best_draft(self, worker_drafts: List[Dict[str, Any]], 
                          available_agents: List[str]) -> Dict[str, Any]:
        """选择最好的draft"""
        
        valid_drafts = []
        for i, draft in enumerate(worker_drafts):
            if isinstance(draft, Exception):
                continue
            if draft['success'] and draft['content']:
                valid_drafts.append(draft)
        
        if not valid_drafts:
            return {
                'agent_id': available_agents[0],
                'content': "Failed to generate draft",
                'success': False
            }
        
        # 简单选择：选择最长的非错误draft（通常更完整）
        best_draft = max(valid_drafts, key=lambda x: len(x['content']))
        return best_draft
    
    async def _worker_annotation_phase(self, draft: SimplifiedDraft,
                                     available_agents: List[str]) -> SimplifiedDraft:
        """阶段2: Worker简单批注协作"""

        print("   📋 Phase 2: Worker Collaborative Annotations")

        # 简单的Worker批注，每个Worker提供一个简洁的改进建议
        for agent_id in available_agents:
            try:
                # 获取当前其他agent的批注作为peer_annotations
                peer_annotations = [ann for ann in draft.annotations if ann['agent_id'] != agent_id]

                annotation = await self._generate_simple_annotation(agent_id, draft, peer_annotations)
                if annotation and annotation.strip():
                    draft.add_annotation(agent_id, annotation, "suggestion")
                    print(f"      ✅ {agent_id} added annotation")

            except Exception as e:
                print(f"      ❌ {agent_id} annotation failed: {e}")

        print(f"      📊 Worker Annotations: {len(draft.annotations)} suggestions")
        return draft

    async def _generate_simple_annotation(self, agent_id: str, draft: SimplifiedDraft,
                                        peer_annotations: List[Dict]) -> str:
        """生成简单的Worker批注建议"""
        try:
            # 简单的批注提示
            peer_summary = ""
            if peer_annotations:
                peer_summary = "\n".join([f"- {ann['agent_id']}: {ann['text'][:100]}" for ann in peer_annotations[-2:]])

            prompt = f"""Review this draft and provide ONE brief improvement suggestion (max 50 words):

DRAFT:
{draft.content}

PEER SUGGESTIONS:
{peer_summary}

Your suggestion (be concise and specific):"""

            # 调用LLM
            response = await async_generate_completion(
                agent_id=agent_id,
                prompt=prompt,
                max_tokens=80,  # 限制长度，强制简洁
                temperature=0.3
            )

            return response.strip() if response else ""

        except Exception as e:
            print(f"      ⚠️ {agent_id} annotation generation failed: {e}")
            return ""

    async def _generate_annotation_with_leader_tasks(self, agent_id: str, draft: SimplifiedDraft,
                                                   peer_annotations: List[Dict], leader_tasks: List[Dict]) -> str:
        """生成带有Leader定向任务和同侪批注可见性的批注"""
        
        # 构建Leader定向任务摘要
        leader_guidance = ""
        if leader_tasks:
            high_priority_tasks = [task for task in leader_tasks if task['type'] in ['high_priority_fix', 'targeted_improvement']]
            general_guidance = [task for task in leader_tasks if task['type'] in ['improvement_guidance', 'dimension_guidance']]
            
            if high_priority_tasks:
                leader_guidance += "\n\nLEADER PRIORITY TASKS FOR YOU:\n"
                for i, task in enumerate(high_priority_tasks, 1):
                    leader_guidance += f"{i}. {task['text']}\n"
            
            if general_guidance:
                leader_guidance += "\nLEADER GENERAL GUIDANCE:\n"
                for task in general_guidance:
                    leader_guidance += f"- {task['text'][:150]}\n"
            
            leader_guidance += "\nPRIORITIZE addressing Leader's specific feedback above."
        
        # 构建同侪批注摘要
        peer_summary = ""
        if peer_annotations:
            peer_summary = "\n\nPEER SUGGESTIONS YOU CAN SEE:\n"
            for i, ann in enumerate(peer_annotations, 1):
                peer_summary += f"{i}. {ann['agent_id']}: {ann['text'][:100]}\n"
            peer_summary += "\nIf you disagree with peers, provide a counter-annotation. If you agree, you can reference their suggestion."
        
        prompt = f"""Analyze this draft and provide ONE brief, actionable suggestion for improvement.

PROBLEM: {draft.question}
TASK TYPE: {draft.task_type}
CURRENT DRAFT: {draft.content}{leader_guidance}{peer_summary}

Focus priorities (in order):
1. ADDRESS LEADER'S SPECIFIC FEEDBACK FIRST (if any)
2. Accuracy and correctness
3. Missing steps or information  
4. Format compliance
5. Consider peers' suggestions but provide your independent analysis

Provide only ONE specific suggestion (max 50 words):"""
        
        try:
            system_prompt = get_system_prompt(draft.task_type)
            result = await async_generate_completion(
                agent_id=agent_id,
                prompt=prompt,
                system_prompt=system_prompt,
                max_tokens=100,
                temperature=0.2
            )
            
            return result.strip() if result else ""
            
        except Exception as e:
            print(f"      ❌ {agent_id} annotation generation failed: {e}")
            return ""

    async def _generate_annotation_with_peers(self, agent_id: str, draft: SimplifiedDraft, peer_annotations: List[Dict]) -> str:
        """生成带有同侪批注可见性的批注（向后兼容）"""
        return await self._generate_annotation_with_leader_tasks(agent_id, draft, peer_annotations, [])
    
    async def _generate_consensus_response(self, agent_id: str, draft: SimplifiedDraft, others_annotations: List[Dict]) -> str:
        """生成对同侪批注的共识回应"""
        
        # 构建其他agent批注摘要
        others_summary = "\n\nPEERS' SUGGESTIONS TO RESPOND TO:\n"
        for i, ann in enumerate(others_annotations, 1):
            others_summary += f"[{i}] {ann['agent_id']}: {ann['text']}\n"
        
        prompt = f"""Review your peers' suggestions and provide consensus feedback.

PROBLEM: {draft.question}
CURRENT DRAFT: {draft.content}{others_summary}

Instructions:
- If you AGREE with a peer's suggestion, respond: "AGREE(suggestion_{i}): [brief reason]"
- If you DISAGREE or have improvements, provide: "COUNTER: [your alternative suggestion in max 50 words]"
- Focus on reaching consensus while maintaining quality

Your response:"""
        
        try:
            system_prompt = "You are a collaborative worker seeking consensus with peers while maintaining high standards."
            result = await async_generate_completion(
                agent_id=agent_id,
                prompt=prompt,
                system_prompt=system_prompt,
                max_tokens=120,
                temperature=0.1  # 降低随机性以促进共识
            )
            
            return result.strip() if result else ""
            
        except Exception as e:
            print(f"      ❌ {agent_id} consensus response failed: {e}")
            return ""

    def _analyze_consensus_and_get_conflicts(self, draft: SimplifiedDraft, consensus_responses: Dict[str, str], available_agents: List[str]) -> List[str]:
        """分析共识情况并返回需要进一步讨论的冲突Workers"""
        
        # 统计每个span_id的AGREE/COUNTER情况
        span_stats = {}
        conflict_workers = set()
        
        # 分析初始批注作为span_id基础
        for i, ann in enumerate(draft.annotations):
            if ann['type'] == 'suggestion':  # 只分析初始建议
                span_id = f"suggestion_{i+1}"
                span_stats[span_id] = {
                    'total_responses': 0,
                    'agree_count': 0,
                    'counter_count': 0,
                    'agree_agents': [],
                    'counter_agents': [],
                    'original_agent': ann['agent_id']
                }
        
        # 分析共识回应
        for agent_id, response in consensus_responses.items():
            if not response:
                continue
                
            if response.startswith("AGREE"):
                # 提取AGREE(suggestion_X)中的X
                import re
                match = re.search(r'AGREE\(suggestion_(\d+)\)', response)
                if match:
                    span_id = f"suggestion_{match.group(1)}"
                    if span_id in span_stats:
                        span_stats[span_id]['total_responses'] += 1
                        span_stats[span_id]['agree_count'] += 1
                        span_stats[span_id]['agree_agents'].append(agent_id)
                        
            elif response.startswith("COUNTER"):
                # COUNTER表示对所有建议都有异议
                for span_id in span_stats:
                    span_stats[span_id]['total_responses'] += 1
                    span_stats[span_id]['counter_count'] += 1
                    span_stats[span_id]['counter_agents'].append(agent_id)
                    conflict_workers.add(agent_id)
        
        # 检查是否达到2/3一致
        total_agents = len(available_agents)
        required_consensus = max(2, int(total_agents * 2 / 3))  # 至少2个，或者2/3
        
        print(f"      📈 Consensus Analysis (need {required_consensus}/{total_agents} agreement):")
        
        for span_id, stats in span_stats.items():
            agree_ratio = stats['agree_count'] / total_agents if total_agents > 0 else 0
            print(f"         {span_id}: {stats['agree_count']} agree, {stats['counter_count']} counter (ratio: {agree_ratio:.2f})")
            
            if stats['agree_count'] < required_consensus:
                # 未达到共识，标记相关的counter agents为冲突
                conflict_workers.update(stats['counter_agents'])
                conflict_workers.add(stats['original_agent'])  # 原建议者也参与讨论
        
        return list(conflict_workers)
    
    async def _resolve_conflicts_discussion(self, draft: SimplifiedDraft, conflict_workers: List[str]):
        """让冲突Workers进行专门的讨论轮"""
        
        print(f"      🔥 Conflict resolution discussion with: {conflict_workers}")
        
        # 获取冲突的具体内容
        conflict_summary = self._build_conflict_summary(draft)
        
        # 让冲突Workers进行集中讨论
        for agent_id in conflict_workers:
            try:
                resolution_response = await self._generate_conflict_resolution_response(
                    agent_id, draft, conflict_summary, conflict_workers
                )
                if resolution_response:
                    draft.add_annotation(agent_id, resolution_response, "conflict_resolution")
                    print(f"      🔧 {agent_id} provided conflict resolution")
            except Exception as e:
                print(f"      ❌ {agent_id} conflict resolution failed: {e}")
    
    def _build_conflict_summary(self, draft: SimplifiedDraft) -> str:
        """构建冲突摘要"""
        conflicts = []
        suggestion_count = 0
        counter_count = 0
        
        for ann in draft.annotations:
            if ann['type'] == 'suggestion':
                suggestion_count += 1
                conflicts.append(f"Suggestion {suggestion_count}: {ann['text'][:100]}")
            elif ann['type'] == 'consensus_response' and ann['text'].startswith("COUNTER"):
                counter_count += 1
                conflicts.append(f"Counter {counter_count}: {ann['text'][:100]}")
        
        return "\n".join(conflicts)
    
    async def _generate_conflict_resolution_response(self, agent_id: str, draft: SimplifiedDraft, 
                                                   conflict_summary: str, conflict_workers: List[str]) -> str:
        """生成冲突解决回应"""
        
        prompt = f"""You are in a conflict resolution discussion. Help reach consensus on the disputed suggestions.

PROBLEM: {draft.question}
CURRENT DRAFT: {draft.content}

CONFLICTS TO RESOLVE:
{conflict_summary}

PARTICIPANTS IN THIS DISCUSSION: {', '.join(conflict_workers)}

Your task:
1. Identify the core disagreement
2. Propose a compromise solution that addresses everyone's concerns
3. Be specific and constructive

Provide a compromise suggestion (max 80 words):"""
        
        try:
            system_prompt = "You are a diplomatic problem-solver focused on finding win-win solutions."
            result = await async_generate_completion(
                agent_id=agent_id,
                prompt=prompt,
                system_prompt=system_prompt,
                max_tokens=150,
                temperature=0.1  # 低随机性促进一致性
            )
            
            return result.strip() if result else ""
            
        except Exception as e:
            print(f"      ❌ {agent_id} conflict resolution generation failed: {e}")
            return ""

    async def _add_leader_feedback_as_worker_tasks(self, draft: SimplifiedDraft, evaluation: Dict[str, Any]):
        """将Leader的结构化反馈转换为Worker定向任务"""
        
        # 1. 添加总体改进指导
        overall_feedback = f"Leader Quality Assessment - Overall: {evaluation.get('overall', 0.6):.2f}"
        dimensions = []
        for dim in ['accuracy', 'completeness', 'clarity']:
            if dim in evaluation:
                dimensions.append(f"{dim}: {evaluation[dim]:.2f}")
        if dimensions:
            overall_feedback += f" | Dimensions: {', '.join(dimensions)}"
        
        if evaluation.get('improvement_priorities'):
            overall_feedback += f"\nPriority Areas: {', '.join(evaluation['improvement_priorities'])}"
        
        draft.add_annotation("leader", overall_feedback, "improvement_guidance")
        print(f"      📌 Added overall improvement guidance")
        
        # 2. 添加具体的结构化反馈任务
        if evaluation.get('feedback_annotations'):
            print(f"      🎯 Converting {len(evaluation['feedback_annotations'])} feedback items to targeted tasks")
            
            for i, annotation in enumerate(evaluation['feedback_annotations'], 1):
                span_id = annotation.get('span_id', f'issue_{i}')
                issue_type = annotation.get('issue_type', 'general')
                comment = annotation.get('comment', 'No specific comment')
                severity = annotation.get('severity', 'medium')
                
                # 构建定向任务描述
                task_description = f"[{severity.upper()} {issue_type.upper()}] {span_id}: {comment}"
                
                # 根据严重程度选择任务类型
                task_type = "high_priority_fix" if severity == "high" else "targeted_improvement"
                
                draft.add_annotation("leader", task_description, task_type)
                
                severity_icon = {'high': '🔴', 'medium': '🟡', 'low': '🟢'}.get(severity, '🟡')
                print(f"         {i}. {severity_icon} Targeted task: {issue_type} in {span_id}")
        
        # 3. 添加维度特定的改进建议
        dimension_feedback = []
        for dim_name, threshold in [('accuracy', 0.8), ('completeness', 0.75), ('clarity', 0.7)]:
            dim_score = evaluation.get(dim_name, 0.6)
            if dim_score < threshold:
                improvement_msg = f"Focus on improving {dim_name} (current: {dim_score:.2f}, target: {threshold:.2f})"
                dimension_feedback.append(improvement_msg)
        
        if dimension_feedback:
            combined_msg = "Dimension-specific improvements needed:\n" + "\n".join(dimension_feedback)
            draft.add_annotation("leader", combined_msg, "dimension_guidance")
            print(f"      📊 Added dimension-specific guidance for {len(dimension_feedback)} areas")

    async def _generate_annotation(self, agent_id: str, draft: SimplifiedDraft) -> str:
        """生成单个批注（保留向后兼容性）"""
        return await self._generate_annotation_with_peers(agent_id, draft, [])
    
    async def _merger_phase(self, draft: SimplifiedDraft) -> SimplifiedDraft:
        """阶段3: 自适应Merger融合"""

        print("   🔀 Phase 3: Adaptive Merger Integration")

        if not draft.annotations:
            print("      ℹ️ No annotations to merge")
            return draft

        import time
        merge_start_time = time.time()

        try:
            # 1. 转换注释格式为DraftAnnotation（为了兼容性）
            draft_annotations = []
            for ann in draft.annotations:
                # 创建临时的DraftAnnotation对象
                temp_ann = type('DraftAnnotation', (), {
                    'id': f"ann_{len(draft_annotations)}",
                    'agent_id': ann['agent_id'],
                    'annotation_text': ann['text'],
                    'annotation_type': ann['type'],
                    'target_text': draft.content[:100],  # 简化的目标文本
                    'priority': 1,
                    'consensus_score': 0.8  # 默认共识分数
                })()
                draft_annotations.append(temp_ann)

            # 2. 创建临时的SharedDraft对象（为了兼容性）
            temp_shared_draft = type('SharedDraft', (), {
                'current_content': draft.content,
                'question_content': draft.question,
                'question_type': draft.task_type
            })()

            # 3. 分析批注并推荐策略
            print("      📊 Analyzing annotations for optimal merge strategy...")
            analysis = await self.strategy_selector.analyze_annotations(draft_annotations, temp_shared_draft)
            recommendation = await self.strategy_selector.recommend_strategy(analysis, temp_shared_draft)

            print(f"      🎯 Selected strategy: {recommendation.recommended_strategy.value}")
            print(f"      📈 Confidence: {recommendation.confidence:.2f}")
            print(f"      💭 Reasoning: {recommendation.reasoning}")

            # 4. 执行合并（使用推荐的策略）
            merge_result = await self._execute_adaptive_merge(
                draft, draft_annotations, recommendation.recommended_strategy
            )

            # 5. 记录合并元数据
            merge_time = time.time() - merge_start_time
            merge_metadata = self.strategy_selector.get_merge_metadata(analysis, recommendation)
            merge_metadata['execution_time'] = merge_time
            merge_metadata['success'] = merge_result['success']
            merge_metadata['quality_score'] = merge_result.get('quality_score', 0.8)

            self.merge_statistics.append(merge_metadata)

            # 6. 记录策略性能
            self.strategy_selector.record_strategy_performance(
                recommendation.recommended_strategy,
                merge_result['success'],
                merge_time,
                merge_result.get('quality_score', 0.8)
            )

            # 7. 更新draft内容
            if merge_result['success'] and merge_result.get('merged_content'):
                draft.update_content(merge_result['merged_content'], "adaptive_merger", "adaptive_fusion")
                print(f"      ✅ Adaptive merge completed successfully")
            else:
                print(f"      ⚠️ Merger failed, keeping original")

        except Exception as e:
            print(f"      ❌ Merger phase failed: {e}")

        return draft

    async def _execute_adaptive_merge(self, draft: SimplifiedDraft, annotations: List,
                                    strategy) -> Dict[str, Any]:
        """执行自适应合并"""

        try:
            # 准备批注文本
            annotations_text = "\n".join([
                f"- {ann.agent_id}: {ann.annotation_text}"
                for ann in annotations
            ])

            # 使用语义合成策略（简化版）
            result = await self._semantic_merge(draft, annotations_text)

            return {
                'success': True,
                'merged_content': result,
                'strategy_used': 'semantic_synthesis',
                'quality_score': 0.8  # 简化的质量评分
            }

        except Exception as e:
            return {
                'success': False,
                'merged_content': draft.content,
                'error': str(e),
                'quality_score': 0.3
            }

    async def _semantic_merge(self, draft: SimplifiedDraft, annotations_text: str) -> str:
        """语义合成策略"""
        from prompt import get_merger_prompt, get_worker_draft_prompt

        prompt = get_merger_prompt(
            'merge_annotations',
            question=draft.question,
            task_type=draft.task_type,
            annotated_drafts=f"CURRENT DRAFT:\n{draft.content}\n\nANNOTATIONS:\n{annotations_text}",
            task_prompt=get_worker_draft_prompt(draft.task_type)
        )

        return await self._call_merge_agent(prompt, draft.task_type)

    async def _call_merge_agent(self, prompt: str, task_type: str) -> str:
        """调用合并代理"""
        from prompt import get_system_prompt

        merger_agent = get_config("merge_agent_model", "gemini")
        system_prompt = get_system_prompt(task_type)

        result = await async_generate_completion(
            agent_id=merger_agent,
            prompt=prompt,
            system_prompt=system_prompt,
            max_tokens=1024,
            temperature=0.2
        )

        return result if result else ""

        for ann in draft.annotations:
            if ann['text'].startswith('PATCH('):
                try:
                    # 解析PATCH格式: PATCH(span_id,new_text,why)
                    import re
                    match = re.match(r'PATCH\(([^,]+),([^,]+),(.+)\)', ann['text'])
                    if match:
                        span_id, new_text, why = match.groups()
                        # 简单的文本替换 - 在实际应用中可以更智能
                        # 这里简化为在内容末尾添加改进
                        draft.content += f"\n[PATCH {span_id}]: {new_text.strip()}"
                        patch_count += 1
                        print(f"      🔧 Applied patch for {span_id}: {new_text.strip()}")
                except Exception as e:
                    print(f"      ⚠️ Failed to apply patch: {e}")

        return patch_count

    def _identify_complementary_annotations(self, draft: SimplifiedDraft) -> List[Dict]:
        """识别互补的批注（非PATCH类型）"""
        complementary = []

        for ann in draft.annotations:
            # 排除PATCH和PASS，寻找AGREE/COUNTER等需要融合的批注
            if not ann['text'].startswith(('PATCH(', 'PASS')):
                complementary.append(ann)

        return complementary

    async def _creative_merge(self, draft: SimplifiedDraft, annotations: List[Dict]):
        """CreativeMerge使用mini模型，qwen做备份"""
        try:
            # 准备批注文本
            annotations_text = "\n".join([
                f"- {ann['agent_id']}: {ann['text']}"
                for ann in annotations
            ])

            prompt = f"""Creatively merge these complementary suggestions into the draft:

CURRENT DRAFT:
{draft.content}

COMPLEMENTARY SUGGESTIONS:
{annotations_text}

Intelligently combine the suggestions to improve the draft:"""

            # 首先尝试mini模型
            try:
                result = await async_generate_completion(
                    agent_id="openai",  # gpt-4o-mini
                    prompt=prompt,
                    max_tokens=1024,
                    temperature=0.3
                )

                if result and len(result.strip()) > 50:
                    draft.update_content(result, "creative_merger", "creative_fusion")
                    print(f"      ✅ CreativeMerge completed with mini model")
                    return

            except Exception as e:
                print(f"      ⚠️ Mini model failed: {e}")

            # 备份：使用qwen
            try:
                result = await async_generate_completion(
                    agent_id="qwen",
                    prompt=prompt,
                    max_tokens=1024,
                    temperature=0.3
                )

                if result:
                    draft.update_content(result, "creative_merger_qwen", "creative_fusion_backup")
                    print(f"      ✅ CreativeMerge completed with qwen backup")

            except Exception as e:
                print(f"      ❌ Both mini and qwen failed: {e}")

        except Exception as e:
            print(f"      ❌ CreativeMerge failed: {e}")

            # 2. 创建临时的SharedDraft对象（为了兼容性）
            temp_shared_draft = type('SharedDraft', (), {
                'current_content': draft.content,
                'question_content': draft.question,
                'question_type': draft.task_type
            })()

            # 3. 分析批注并推荐策略
            print("      📊 Analyzing annotations for optimal merge strategy...")
            analysis = await self.strategy_selector.analyze_annotations(draft_annotations, temp_shared_draft)
            recommendation = await self.strategy_selector.recommend_strategy(analysis, temp_shared_draft)

            print(f"      🎯 Selected strategy: {recommendation.recommended_strategy.value}")
            print(f"      📈 Confidence: {recommendation.confidence:.2f}")
            print(f"      💭 Reasoning: {recommendation.reasoning}")

            # 4. 执行合并（使用推荐的策略）
            merge_result = await self._execute_adaptive_merge(
                draft, draft_annotations, recommendation.recommended_strategy
            )

            # 5. 记录合并元数据
            merge_time = time.time() - merge_start_time
            merge_metadata = self.strategy_selector.get_merge_metadata(analysis, recommendation)
            merge_metadata['execution_time'] = merge_time
            merge_metadata['success'] = merge_result['success']
            merge_metadata['quality_score'] = merge_result.get('quality_score', 0.8)

            self.merge_statistics.append(merge_metadata)

            # 6. 记录策略性能
            self.strategy_selector.record_strategy_performance(
                recommendation.recommended_strategy,
                merge_result['success'],
                merge_time,
                merge_result.get('quality_score', 0.8)
            )

            # 7. 更新draft内容
            if merge_result['success'] and merge_result.get('merged_content'):
                draft.update_content(merge_result['merged_content'], "adaptive_merger", "adaptive_fusion")
                print(f"      ✅ Adaptive merge completed successfully")
            else:
                print(f"      ⚠️ Merger failed, keeping original")
                
        except Exception as e:
            print(f"      ❌ Merger phase failed: {e}")
        
        return draft

    async def _execute_adaptive_merge(self, draft: SimplifiedDraft, annotations: List,
                                    strategy: MergeStrategy) -> Dict[str, Any]:
        """执行自适应合并"""

        try:
            # 准备批注文本
            annotations_text = "\n".join([
                f"- {ann.agent_id}: {ann.annotation_text}"
                for ann in annotations
            ])

            # 根据策略选择不同的合并方法
            if strategy == MergeStrategy.SEQUENTIAL_INTEGRATION:
                result = await self._sequential_merge(draft, annotations_text)
            elif strategy == MergeStrategy.SEMANTIC_SYNTHESIS:
                result = await self._semantic_merge(draft, annotations_text)
            elif strategy == MergeStrategy.CONFLICT_RESOLUTION:
                result = await self._conflict_resolution_merge(draft, annotations_text)
            elif strategy == MergeStrategy.PRIORITY_BASED:
                result = await self._priority_based_merge(draft, annotations, annotations_text)
            elif strategy == MergeStrategy.CREATIVE_COMBINATION:
                result = await self._creative_merge(draft, annotations_text)
            else:
                # 默认使用语义合成
                result = await self._semantic_merge(draft, annotations_text)

            return {
                'success': True,
                'merged_content': result,
                'strategy_used': strategy.value,
                'quality_score': 0.8  # 简化的质量评分
            }

        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'merged_content': draft.content,  # 返回原内容
                'strategy_used': strategy.value,
                'quality_score': 0.3
            }

    async def _sequential_merge(self, draft: SimplifiedDraft, annotations_text: str) -> str:
        """顺序合并策略"""
        prompt = f"""Apply these annotations to the draft in sequence, one by one:

Current Draft:
{draft.content}

Annotations to apply:
{annotations_text}

Apply each annotation carefully and return the improved draft:"""

        return await self._call_merge_agent(prompt, draft.task_type)

    async def _semantic_merge(self, draft: SimplifiedDraft, annotations_text: str) -> str:
        """语义合成策略"""
        prompt = get_merger_prompt(
            'merge_annotations',
            question=draft.question,
            task_type=draft.task_type,
            annotated_drafts=f"CURRENT DRAFT:\n{draft.content}\n\nANNOTATIONS:\n{annotations_text}",
            task_prompt=get_worker_draft_prompt(draft.task_type)
        )

        return await self._call_merge_agent(prompt, draft.task_type)

    async def _conflict_resolution_merge(self, draft: SimplifiedDraft, annotations_text: str) -> str:
        """冲突解决策略"""
        prompt = f"""Resolve conflicts between these annotations and merge them intelligently:

Current Draft:
{draft.content}

Conflicting Annotations:
{annotations_text}

Identify conflicts, resolve them using best judgment, and return the improved draft:"""

        return await self._call_merge_agent(prompt, draft.task_type)

    async def _priority_based_merge(self, draft: SimplifiedDraft, annotations: List, annotations_text: str) -> str:
        """优先级导向策略"""
        # 按优先级排序注释
        sorted_annotations = sorted(annotations, key=lambda x: getattr(x, 'priority', 1), reverse=True)
        priority_text = "\n".join([
            f"- Priority {getattr(ann, 'priority', 1)}: {ann.agent_id}: {ann.annotation_text}"
            for ann in sorted_annotations
        ])

        prompt = f"""Apply these annotations in priority order (highest first):

Current Draft:
{draft.content}

Prioritized Annotations:
{priority_text}

Focus on high-priority suggestions first, then incorporate lower-priority ones if compatible:"""

        return await self._call_merge_agent(prompt, draft.task_type)

    async def _creative_merge(self, draft: SimplifiedDraft, annotations_text: str) -> str:
        """创造性组合策略"""
        prompt = f"""Creatively synthesize these diverse annotations into an improved draft:

Current Draft:
{draft.content}

Diverse Annotations:
{annotations_text}

Use creative thinking to combine different perspectives and create a superior solution:"""

        return await self._call_merge_agent(prompt, draft.task_type)

    async def _call_merge_agent(self, prompt: str, task_type: str) -> str:
        """调用合并代理"""
        merger_agent = get_config("merge_agent_model", "deepseek")
        system_prompt = get_system_prompt(task_type)

        result = await async_generate_completion(
            agent_id=merger_agent,
            prompt=prompt,
            system_prompt=system_prompt,
            max_tokens=1024,
            temperature=0.2
        )

        return result if result else ""

    async def _context_compression_phase(self, draft: SimplifiedDraft) -> SimplifiedDraft:
        """阶段0: 动态上下文压缩"""

        print("   🗜️ Phase 0: Context Compression")

        try:
            # 1. 准备上下文数据
            context_data = {
                'current_content': draft.content,
                'annotations': draft.annotations,
                'change_history': draft.history,
                'question': draft.question,
                'task_type': draft.task_type,
                'session_id': draft.session_id,
                'version': draft.version,
                'participants': draft.participants,
                'quality_score': draft.quality_score,
                'created_at': draft.created_at.isoformat()
            }

            # 2. 估算压缩需求
            compression_estimate = self.context_compressor.estimate_compression_need(context_data)

            if not compression_estimate['compression_needed']:
                print("      ✅ Context within budget, no compression needed")
                return draft

            print(f"      📊 Context usage: {compression_estimate['current_usage']}/{compression_estimate['budget_limit']} tokens")
            print(f"      🎯 Excess tokens: {compression_estimate['excess_tokens']}")

            # 3. 执行压缩
            compressed_context = await self.context_compressor.compress_context(context_data)

            # 4. 更新draft内容
            if 'current_content' in compressed_context:
                old_content = draft.content
                draft.content = compressed_context['current_content']

                # 记录压缩变更
                if old_content != draft.content:
                    draft.history.append({
                        'version': draft.version,
                        'content': old_content,
                        'agent': 'context_compressor',
                        'change_type': 'compression',
                        'timestamp': datetime.now().isoformat()
                    })
                    draft.version += 1

            # 5. 更新注释（如果被压缩）
            if 'annotations' in compressed_context and isinstance(compressed_context['annotations'], list):
                draft.annotations = compressed_context['annotations']

            # 6. 更新历史（如果被压缩）
            if 'change_history' in compressed_context and isinstance(compressed_context['change_history'], list):
                draft.history = compressed_context['change_history']

            # 7. 记录压缩统计
            compression_stats = self.context_compressor.get_compression_statistics()
            self.compression_statistics.append({
                'round': len(self.compression_statistics) + 1,
                'timestamp': datetime.now().isoformat(),
                'original_tokens': compression_estimate['current_usage'],
                'compressed_tokens': compression_estimate['current_usage'] - compression_estimate['excess_tokens'],
                'compression_ratio': compression_stats.get('average_compression_ratio', 1.0),
                'tokens_saved': compression_estimate['excess_tokens']
            })

            print(f"      ✅ Context compressed successfully")

        except Exception as e:
            print(f"      ⚠️ Context compression failed: {e}")
            # 压缩失败时继续使用原draft

        return draft

    async def _leader_evaluation_and_final_answer_phase(self, draft: SimplifiedDraft) -> Dict[str, Any]:
        """阶段4: Leader评估并生成最终答案"""
        
        print("   🎯 Phase 4: Leader Evaluation & Final Answer Generation")
        
        try:
            # 第1步：评估当前draft质量 - 多维度评分
            evaluation_prompt = self._create_multidimensional_evaluation_prompt(draft)

            leader_agent = get_config("leader_model", "deepseek")
            system_prompt = get_system_prompt(draft.task_type)

            evaluation_result = await async_generate_completion(
                agent_id=leader_agent,
                prompt=evaluation_prompt,
                system_prompt=system_prompt,
                max_tokens=512,
                temperature=0.1
            )

            evaluation = self._parse_leader_evaluation(evaluation_result)

            # 第2步：如果需要，生成最终简洁答案
            final_answer = None
            if evaluation['decision'] == 'approved':
                # 生成最终答案
                final_answer_prompt = self._create_final_answer_prompt(draft)

                final_answer_result = await async_generate_completion(
                    agent_id=leader_agent,
                    prompt=final_answer_prompt,
                    system_prompt=system_prompt,
                    max_tokens=256,  # 保持简洁
                    temperature=0.1
                )

                final_answer = final_answer_result.strip() if final_answer_result else None
                print(f"      ✅ Leader generated final answer")

                # 将最终答案存储在evaluation中
                evaluation['final_answer'] = final_answer
            
            print(f"      📊 Quality Dimensions:")
            print(f"         📍 Accuracy: {evaluation.get('accuracy', 0.6):.2f}")
            print(f"         📋 Completeness: {evaluation.get('completeness', 0.6):.2f}")
            print(f"         💡 Clarity: {evaluation.get('clarity', 0.6):.2f}")
            print(f"         🎯 Overall: {evaluation.get('overall', 0.6):.2f}")
            print(f"      🎯 Decision: {evaluation['decision']}")
            
            # 显示具体改进建议
            if evaluation.get('feedback_annotations'):
                print(f"      📋 Specific Feedback ({len(evaluation['feedback_annotations'])} items):")
                for i, annotation in enumerate(evaluation['feedback_annotations'], 1):
                    severity_icon = {'high': '🔴', 'medium': '🟡', 'low': '🟢'}.get(annotation.get('severity', 'medium'), '🟡')
                    print(f"         {i}. {severity_icon} {annotation.get('span_id', 'N/A')}: {annotation.get('comment', 'No comment')}")
            
            if evaluation.get('improvement_priorities'):
                print(f"      🔧 Priority Improvements: {', '.join(evaluation['improvement_priorities'][:3])}")
            
            return {
                'evaluation': evaluation,
                'final_answer': final_answer
            }
            
        except Exception as e:
            print(f"      ❌ Leader evaluation and final answer generation failed: {e}")
            return {
                'evaluation': {
                    'overall_score': 0.5,
                    'quality_score': 0.5,
                    'decision': 'needs_major_revision',
                    'feedback': f"Leader processing failed: {str(e)}"
                },
                'final_answer': None
            }
    
    def _create_final_answer_prompt(self, draft: SimplifiedDraft) -> str:
        """创建Leader生成最终答案的prompt - 每种题库有特定的输出要求"""
        
        # 每种题库的具体输出要求
        task_instructions = {
            'gsm8k': {
                'format': '#### [number]',
                'requirement': 'Extract ONLY the final numerical answer (no units, no explanations)',
                'example': '#### 42'
            },
            'math': {
                'format': '#### [mathematical_expression_or_number]', 
                'requirement': 'Extract ONLY the final mathematical answer (expression, number, or formula)',
                'example': '#### 3x^2 + 2x - 1'
            },
            'mbpp': {
                'format': 'Complete Python function code',
                'requirement': 'Output ONLY the complete Python function code, starting with def',
                'example': 'def is_even(n):\n    return n % 2 == 0'
            },
            'humaneval': {
                'format': 'Complete Python function code',
                'requirement': 'Output ONLY the complete Python function code, starting with def', 
                'example': 'def max_element(lst):\n    return max(lst) if lst else None'
            },
            'drop': {
                'format': '#### [specific_factual_answer]',
                'requirement': 'Extract ONLY the specific factual answer (number, name, date, etc.)',
                'example': '#### 1995'
            },
            'hotpotqa': {
                'format': '#### [specific_factual_answer]',
                'requirement': 'Extract ONLY the specific factual answer (name, date, place, etc.)',
                'example': '#### Barack Obama'
            },
            'strategyqa': {
                'format': '#### [yes/no]',
                'requirement': 'Output ONLY yes or no',
                'example': '#### yes'
            },
            'gpqa': {
                'format': '#### [A/B/C/D]',
                'requirement': 'Output ONLY the correct choice letter',
                'example': '#### B'
            },
            'mmlu': {
                'format': '#### [A/B/C/D]',
                'requirement': 'Output ONLY the correct choice letter',
                'example': '#### C'
            }
        }
        
        task_info = task_instructions.get(draft.task_type, {
            'format': '#### [answer]',
            'requirement': 'Extract ONLY the final answer',
            'example': '#### answer'
        })
        
        return f"""As the leader agent, you must synthesize the collaborative draft into the final, most concise answer.

PROBLEM: {draft.question}
TASK TYPE: {draft.task_type}

COLLABORATIVE DRAFT:
{draft.content}

RECENT ANNOTATIONS:
{chr(10).join([f"- {ann['agent_id']}: {ann['text']}" for ann in draft.annotations[-3:]])}

CRITICAL OUTPUT REQUIREMENTS FOR {draft.task_type.upper()}:
- {task_info['requirement']}
- Format: {task_info['format']}
- Example: {task_info['example']}

IMPORTANT RULES:
1. Be extremely concise - provide ONLY the final answer
2. Follow the exact format required for {draft.task_type}
3. Remove ALL explanations, reasoning, or extra text
4. Ensure accuracy based on the collaborative work
5. For programming tasks: output complete, working code
6. For math tasks: output only the final numerical result or expression
7. For factual questions: output only the specific fact requested
8. For multiple choice: output only the letter choice

Generate the final answer now:"""
    
    def _create_multidimensional_evaluation_prompt(self, draft: SimplifiedDraft) -> str:
        """创建多维度质量评估prompt"""
        
        # 根据任务类型定制评估重点
        task_focus = {
            'gsm8k': 'mathematical reasoning accuracy and calculation correctness',
            'math': 'mathematical rigor, proof validity, and solution completeness',
            'mbpp': 'code functionality, efficiency, and edge case handling',
            'humaneval': 'code correctness, documentation, and implementation quality',
            'drop': 'factual accuracy and evidence-based reasoning',
            'hotpotqa': 'multi-hop reasoning and fact verification',
            'strategyqa': 'logical reasoning and step-by-step analysis',
            'gpqa': 'scientific accuracy and expert-level reasoning',
            'mmlu': 'domain knowledge and comprehensive understanding'
        }
        
        focus_area = task_focus.get(draft.task_type, 'general problem-solving and reasoning')
        
        return f"""You are a quality assessment expert. Evaluate this draft solution across multiple dimensions and provide structured feedback.

PROBLEM: {draft.question}
TASK TYPE: {draft.task_type}
FOCUS AREA: {focus_area}

CURRENT DRAFT:
{draft.content}

COLLABORATIVE ANNOTATIONS SUMMARY:
{self._format_annotations_summary(draft)}

Evaluate the draft on these dimensions (0.0-1.0 scale):

1. ACCURACY: Correctness of facts, calculations, logic, and conclusions
2. COMPLETENESS: All necessary steps, components, and requirements addressed  
3. CLARITY: Clear explanation, good structure, easy to understand
4. OVERALL: Weighted combination considering task requirements

For issues found, provide specific feedback_annotations with:
- span_id: which part needs improvement (e.g., "line_1", "calculation_step_2") 
- issue_type: accuracy/completeness/clarity/format
- comment: specific actionable feedback
- severity: high/medium/low

Output in JSON format:
{{
    "accuracy": 0.0-1.0,
    "completeness": 0.0-1.0, 
    "clarity": 0.0-1.0,
    "overall": 0.0-1.0,
    "decision": "approved" | "needs_minor_revision" | "needs_major_revision",
    "feedback_annotations": [
        {{
            "span_id": "specific_location",
            "issue_type": "accuracy|completeness|clarity|format",
            "comment": "specific actionable feedback",
            "severity": "high|medium|low"
        }}
    ],
    "strengths": ["what the draft does well"],
    "improvement_priorities": ["ordered list of key improvements needed"]
}}"""

    def _format_annotations_summary(self, draft: SimplifiedDraft) -> str:
        """格式化批注摘要供Leader参考"""
        if not draft.annotations:
            return "No annotations available"
        
        summary_lines = []
        for ann in draft.annotations:
            ann_type = ann['type']
            agent = ann['agent_id']
            text = ann['text'][:80] + "..." if len(ann['text']) > 80 else ann['text']
            summary_lines.append(f"- {ann_type.upper()} ({agent}): {text}")
        
        return "\n".join(summary_lines)

    def _parse_leader_evaluation(self, result: str) -> Dict[str, Any]:
        """解析Leader多维度评估结果"""

        # 默认多维度评估结果
        evaluation = {
            'accuracy': 0.6,
            'completeness': 0.6,
            'clarity': 0.6,
            'overall': 0.6,
            'decision': 'needs_minor_revision',
            'feedback': result or 'No feedback provided',
            'feedback_annotations': [],
            'strengths': [],
            'improvement_priorities': []
        }
        
        try:
            # 尝试解析JSON
            if '```json' in result:
                json_start = result.find('```json') + 7
                json_end = result.find('```', json_start)
                json_str = result[json_start:json_end].strip()
                parsed = json.loads(json_str)
                evaluation.update(parsed)

                # 显示结构化反馈
                if 'feedback_annotations' in parsed and parsed['feedback_annotations']:
                    print(f"      📋 Structured Feedback:")
                    for i, annotation in enumerate(parsed['feedback_annotations'], 1):
                        span_id = annotation.get('span_id', 'N/A')
                        issue_type = annotation.get('issue_type', 'general')
                        comment = annotation.get('comment', 'No comment')
                        severity = annotation.get('severity', 'medium')

                        severity_icon = {'high': '🔴', 'medium': '🟡', 'low': '🟢'}.get(severity, '🟡')
                        print(f"         {i}. {severity_icon} Span {span_id} ({issue_type}): {comment}")

            elif '{' in result:
                # 简单JSON解析
                start = result.find('{')
                end = result.rfind('}') + 1
                json_str = result[start:end]
                parsed = json.loads(json_str)
                evaluation.update(parsed)
        except Exception as e:
            print(f"      ⚠️ Failed to parse leader evaluation JSON: {e}")
            pass
        
        # 简单解析文本
        if result:
            lines = result.lower().split('\n')
            for line in lines:
                if 'overall_score' in line or 'score' in line:
                    import re
                    scores = re.findall(r'[\d.]+', line)
                    if scores:
                        score = min(float(scores[0]), 1.0)
                        evaluation['overall_score'] = score
                        evaluation['quality_score'] = score
                
                if 'approved' in line:
                    evaluation['decision'] = 'approved'
                elif 'rejected' in line:
                    evaluation['decision'] = 'rejected'
        
        # 确保quality_score存在，兼容新旧格式
        if 'quality_score' not in evaluation:
            evaluation['quality_score'] = evaluation.get('overall', evaluation.get('overall_score', 0.6))
        
        return evaluation
    
    def _create_final_result(self, draft: SimplifiedDraft, evaluation: Dict[str, Any],
                           total_time: float, session_id: str) -> Dict[str, Any]:
        """创建最终结果 - 简化版本，只返回基本信息"""

        # 提取最终答案
        final_answer = self._extract_final_answer(draft.content, draft.task_type)

        # 获取质量分数，兼容新旧格式
        quality_score = evaluation.get('overall', evaluation.get('overall_score', evaluation.get('quality_score', 0.5)))

        result = {
            'success': evaluation['decision'] in ['approved', 'needs_minor_revision'],
            'solution': final_answer,
            'total_processing_time': total_time,
            'mode': 'simplified_collaborative',
            'quality_score': quality_score,
            'quality_decision': evaluation['decision'],
            'session_id': session_id
        }

        # 不再保存额外的结果文件，只保留 draft evolution
        print(f"💾 Result processed: {final_answer}")

        return result

    def _create_final_result(self, draft: SimplifiedDraft, evaluation: Dict[str, Any],
                                          total_time: float, session_id: str, final_answer: str) -> Dict[str, Any]:
        """创建最终结果"""

        # 获取质量分数，兼容新旧格式
        quality_score = evaluation.get('overall', evaluation.get('overall_score', evaluation.get('quality_score', 0.5)))

        result = {
            'success': evaluation['decision'] in ['approved', 'needs_minor_revision'],
            'solution': final_answer,  # 直接使用最终答案
            'total_processing_time': total_time,
            'mode': 'simplified_collaborative',
            'quality_score': quality_score,
            'quality_decision': evaluation['decision'],
            'session_id': session_id
        }

        print(f"💾 Result processed: {final_answer}")

        return result

    async def _leader_synthesis(self, draft: SimplifiedDraft) -> str:
        """使用leader agent合成最终答案 - 双通道输出"""
        try:
            # 首先检查draft是否已经有正确格式的答案
            if draft.task_type in ["gsm8k", "math"] and "####" in draft.content:
                # 直接提取####后的数字
                import re
                match = re.search(r'####\s*(\d+)', draft.content)
                if match:
                    return match.group(1)

            from prompt import LeaderAgentPrompts
            from utils.api import async_generate_completion

            # 使用增强的leader synthesis prompt
            synthesis_prompt = LeaderAgentPrompts.get_enhanced_synthesis_prompt(
                draft.question,
                draft.task_type,
                draft.content
            )

            # 调用LLM进行synthesis
            synthesized = await async_generate_completion(
                agent_id="openai",  # 使用默认的leader agent
                prompt=synthesis_prompt,
                temperature=0.1,  # 低温度确保一致性
                max_tokens=150    # 进一步限制长度
            )

            # 特殊处理编程任务
            if draft.task_type in ["mbpp", "humaneval"]:
                # 提取代码块
                if "```python" in synthesized:
                    # 提取python代码块
                    start = synthesized.find("```python") + 9
                    end = synthesized.find("```", start)
                    if end != -1:
                        code = synthesized[start:end].strip()
                        return code if code else "No code extracted"

                # 如果没有代码块标记，尝试提取函数定义
                lines = synthesized.strip().split('\n')
                code_lines = []
                in_function = False
                for line in lines:
                    if line.strip().startswith('def ') or in_function:
                        in_function = True
                        code_lines.append(line)
                        # 如果遇到空行且已经有内容，可能是函数结束
                        if not line.strip() and code_lines:
                            break

                if code_lines:
                    return '\n'.join(code_lines).strip()

                # 最后备选：返回整个synthesized内容
                return synthesized.strip()

            # 非编程任务的处理
            # 强制提取ANSWER:后面的内容
            if "ANSWER:" in synthesized:
                answer = synthesized.split("ANSWER:")[-1].strip()
                # 清理答案，移除多余的标点和空白
                answer = answer.split('\n')[0].strip()  # 只取第一行
                answer = answer.rstrip('.')  # 移除末尾句号
                return answer if answer else "No answer extracted"

            # 如果没有ANSWER:格式，尝试提取最后一行简短内容
            lines = synthesized.strip().split('\n')
            for line in reversed(lines):
                line = line.strip()
                if line and len(line) < 100 and not line.startswith(('Here', 'The', 'This')):
                    return line

            return synthesized.strip()[:50]  # 最后的备选：前50字符

        except Exception as e:
            print(f"⚠️ Leader synthesis failed: {e}")
            # 如果synthesis失败，回退到原始提取方法
            return self._extract_final_answer(draft.content, draft.task_type)

    def _extract_final_answer(self, content: str, task_type: str) -> str:
        """提取最终答案，根据不同题库类型进行专门处理"""
        
        # 编程题库：需要完整的函数代码
        if task_type in ['mbpp', 'humaneval']:
            return self._extract_programming_answer(content)
        
        # 数学题库：查找 #### 格式的数学答案
        elif task_type in ['gsm8k', 'math']:
            return self._extract_math_answer(content)
        
        # 多选题库：查找 #### 格式的字母选择
        elif task_type in ['gpqa', 'mmlu']:
            return self._extract_multiple_choice_answer(content)
        
        # 是非题库：查找 #### 格式的 yes/no
        elif task_type == 'strategyqa':
            return self._extract_yes_no_answer(content)
        
        # 事实问答题库：查找 #### 格式的事实答案
        elif task_type in ['drop', 'hotpotqa']:
            return self._extract_factual_answer(content)
        
        # 默认处理
        else:
            return self._extract_generic_answer(content)
    
    def _extract_programming_answer(self, content: str) -> str:
        """提取编程题的完整函数代码"""
        lines = content.split('\n')
        
        # 查找 def 开头的函数
        code_lines = []
        in_function = False
        function_indent = 0
        
        for line in lines:
            if line.strip().startswith('def '):
                in_function = True
                function_indent = len(line) - len(line.lstrip())
                code_lines = [line]
            elif in_function:
                # 检查缩进来判断是否还在函数内
                if line.strip() == '':
                    code_lines.append(line)
                elif len(line) - len(line.lstrip()) > function_indent:
                    code_lines.append(line)
                else:
                    # 缩进回到函数级别或更少，函数结束
                    if line.strip():
                        break
        
        if code_lines:
            return '\n'.join(code_lines)
        
        # 如果没找到标准函数，返回包含 def 的所有行
        def_lines = [line for line in lines if 'def ' in line or 'return ' in line]
        if def_lines:
            return '\n'.join(def_lines)
        
        return content
    
    def _extract_math_answer(self, content: str) -> str:
        """提取数学题的数值或表达式答案"""
        # 查找 #### 格式
        if '####' in content:
            lines = content.split('\n')
            for line in lines:
                if line.strip().startswith('####'):
                    answer = line.strip()[4:].strip()
                    return answer if answer else line.strip()
        
        # 查找数字答案
        lines = content.split('\n')
        for line in reversed(lines):
            line = line.strip()
            if line and (line.replace('.', '').replace('-', '').isdigit() or 
                        any(op in line for op in ['+', '-', '*', '/', '^', 'x', '='])):
                return line
        
        return content
    
    def _extract_multiple_choice_answer(self, content: str) -> str:
        """提取多选题的字母答案"""
        # 查找 #### 格式
        if '####' in content:
            lines = content.split('\n')
            for line in lines:
                if line.strip().startswith('####'):
                    answer = line.strip()[4:].strip()
                    if answer in ['A', 'B', 'C', 'D']:
                        return answer
        
        # 查找单独的字母
        lines = content.split('\n')
        for line in reversed(lines):
            line = line.strip()
            if line in ['A', 'B', 'C', 'D']:
                return line
        
        return content
    
    def _extract_yes_no_answer(self, content: str) -> str:
        """提取是非题的 yes/no 答案"""
        # 查找 #### 格式
        if '####' in content:
            lines = content.split('\n')
            for line in lines:
                if line.strip().startswith('####'):
                    answer = line.strip()[4:].strip().lower()
                    if answer in ['yes', 'no']:
                        return answer
        
        # 查找 yes/no
        content_lower = content.lower()
        lines = content_lower.split('\n')
        for line in reversed(lines):
            line = line.strip()
            if line in ['yes', 'no']:
                return line
        
        return content
    
    def _extract_factual_answer(self, content: str) -> str:
        """提取事实问答题的具体答案 - 智能识别真正的答案"""
        import re

        # Priority 1: 查找明确的答案标记
        answer_patterns = [
            r"(?:Answer|ANSWER):\s*(.+?)(?:\n|$)",
            r"(?:Final Answer|FINAL ANSWER):\s*(.+?)(?:\n|$)",
            r"(?:The answer is|Answer is):\s*(.+?)(?:\n|$)",
            r"####\s*(.+?)(?:\n|$)"
        ]

        for pattern in answer_patterns:
            match = re.search(pattern, content, re.IGNORECASE | re.MULTILINE)
            if match:
                answer = match.group(1).strip()
                if answer and len(answer) < 200:  # 避免过长的答案
                    return answer

        # Priority 2: 跳过明显的元信息和协作过程信息
        meta_patterns = [
            r"^(?:Key improvements?|The draft now|Improved draft|Enhanced draft)",
            r"^(?:\d+\.\s*)?(?:Business type|Geographic reach|Entity \d+)",
            r"^(?:Here's|This|The improved|The enhanced)",
            r"^(?:WINNER_[A-Z]|Agent \w+|Consensus)"
        ]

        lines = content.split('\n')
        valid_lines = []

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 跳过元信息
            is_meta = any(re.match(pattern, line, re.IGNORECASE) for pattern in meta_patterns)
            if is_meta:
                continue

            # 跳过过长的分析性内容（可能是推理过程）
            if len(line) > 150:
                continue

            valid_lines.append(line)

        # Priority 3: 查找简短的事实性答案
        for line in reversed(valid_lines[-5:]):  # 检查最后5行有效内容
            # 跳过列表项开头
            if line.startswith(('-', '•', '*')):
                # 但如果是 "- Answer:" 格式，则提取答案部分
                answer_match = re.search(r"-\s*(?:Answer|ANSWER):\s*(.+)", line, re.IGNORECASE)
                if answer_match:
                    return answer_match.group(1).strip()
                continue

            # 查找简短的事实性答案（人名、公司名、日期等）
            if len(line) < 100 and not line.endswith(':'):
                # 检查是否像一个具体答案
                if (re.search(r'\b(?:Group|Company|Corporation)\b', line, re.IGNORECASE) or
                    re.search(r'\b[A-Z][a-z]+ [A-Z][a-z]+\b', line) or  # 人名格式
                    re.search(r'\b(?:January|February|March|April|May|June|July|August|September|October|November|December)\b', line, re.IGNORECASE) or  # 日期
                    re.search(r'\b\d{4}\b', line) or  # 年份
                    re.search(r'\b(?:The|A) [A-Z][a-z]+ [A-Z][a-z]+\b', line)):  # 专有名词
                    return line

        # Priority 4: 如果没有找到明确答案，返回最后一行有效内容
        if valid_lines:
            return valid_lines[-1]

        # 最后的备选：返回原内容的前100字符
        return content[:100].strip()
    
    def _extract_generic_answer(self, content: str) -> str:
        """通用答案提取"""
        # 查找 #### 格式
        if '####' in content:
            lines = content.split('\n')
            for line in lines:
                if line.strip().startswith('####'):
                    answer = line.strip()[4:].strip()
                    return answer if answer else line.strip()
        
        # 返回最后一行非空内容
        lines = content.split('\n')
        for line in reversed(lines):
            line = line.strip()
            if line:
                return line
        
        return content

    def _create_fast_verify_prompt(self, draft: SimplifiedDraft) -> str:
        """创建快速验证提示"""

        if draft.task_type in ["gsm8k", "math"]:
            return f"""Carefully verify this math solution on a scale of 0.0-1.0:

QUESTION: {draft.question}
DRAFT: {draft.content}

CRITICAL VERIFICATION:
1. Check each calculation step for arithmetic errors
2. Verify unit conversions and logic
3. Ensure the final answer makes sense
4. Rate 0.95+ ONLY if ALL calculations are verified correct

Rate the mathematical accuracy (0.0-1.0):
Respond with just the score: 0.XX"""
        else:
            return f"""Quickly evaluate this draft's overall quality on a scale of 0.0-1.0:

QUESTION: {draft.question}
DRAFT: {draft.content}

Rate the overall quality (0.0-1.0) considering accuracy, completeness, and clarity.
Respond with just the score: 0.XX"""

    def _parse_overall_score(self, result: str) -> float:
        """解析整体评分"""
        try:
            import re
            # 寻找0.XX格式的分数
            match = re.search(r'0\.\d+', result)
            if match:
                return float(match.group())

            # 备选：寻找任何小数
            match = re.search(r'\d+\.\d+', result)
            if match:
                score = float(match.group())
                return min(1.0, max(0.0, score))  # 限制在0-1范围

            return 0.6  # 默认分数
        except:
            return 0.6

    def _get_quality_threshold(self, task_type: str) -> float:
        """获取任务类型的质量阈值 - 数学题要求更高"""
        thresholds = {
            'gsm8k': 0.95,      # 提高到0.95，数学题要求高准确率
            'math': 0.95,       # 提高到0.95
            'mbpp': 0.85,       # 编程题也提高
            'humaneval': 0.90,  # 编程题提高
            'drop': 0.80,       # 阅读理解保持
            'hotpotqa': 0.85,   # 推理题稍微提高
            'strategyqa': 0.80, # 策略推理保持
            'gpqa': 0.90,       # 科学题提高
            'mmlu': 0.85,       # 知识题提高
        }
        return thresholds.get(task_type, 0.85)

    def _leader_extract_final_answer(self, draft: SimplifiedDraft) -> str:
        """Leader直接提取最终答案，不需要额外的synthesis步骤"""

        # 数学题：寻找#### 格式
        if draft.task_type in ["gsm8k", "math"]:
            import re
            match = re.search(r'####\s*(\d+)', draft.content)
            if match:
                return match.group(1)

            # 备选：寻找最后的数字
            numbers = re.findall(r'\b\d+\b', draft.content)
            if numbers:
                return numbers[-1]

        # 编程题：返回完整代码
        elif draft.task_type in ["mbpp", "humaneval"]:
            return self._extract_programming_answer(draft.content)

        # 其他题型：提取简洁答案
        else:
            # 寻找明确的答案标记
            content = draft.content.lower()
            if "answer:" in content:
                answer_part = draft.content.split("answer:")[-1].strip()
                return answer_part.split('\n')[0].strip()

            # 寻找最后的简短句子
            lines = draft.content.split('\n')
            for line in reversed(lines):
                line = line.strip()
                if line and len(line) < 100 and not line.startswith(('step', 'calculate', '-')):
                    return line

        # 最后备选：返回draft的最后50字符
        return draft.content.strip()[-50:] if draft.content else "No answer found"