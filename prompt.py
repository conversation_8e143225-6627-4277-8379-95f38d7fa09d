#!/usr/bin/env python3
"""
重新设计的协作式多智能体Prompt系统
基于prompt_old.py的优秀设计，专注于简洁高效的draft协作
"""

# ========================= 基础系统Prompts =========================

SYSTEM_PROMPT = "You are an expert AI assistant. Provide thoughtful, accurate responses in English."

# 数据集特定的系统增强
DATASET_SYSTEM_ENHANCEMENTS = {
    "gsm8k": "Focus on clear arithmetic and step-by-step problem solving. Avoid overcomplicating simple math problems.",
    "math": "Apply rigorous mathematical reasoning. Use appropriate mathematical notation and verify your work.",
    "mbpp": "Write clean, efficient Python code. Consider edge cases and follow Python conventions.",
    "humaneval": "Implement robust solutions that handle all specified requirements and edge cases.",
    "drop": "Carefully read and extract information from the passage. Focus on numerical reasoning and factual accuracy.",
    "hotpotqa": "Use multi-hop reasoning to connect information across passages. First identify question type (bridge/comparison), then extract key entities, map relationships, and verify each reasoning step.",
    "strategyqa": "Consider implicit knowledge and common sense. Think about real-world constraints and logical implications.",
    "gpqa": "Apply graduate-level scientific knowledge. Consider each option carefully using domain expertise.",
    "mmlu": "Draw upon comprehensive knowledge across disciplines. Eliminate incorrect options systematically."
}

# ========================= Worker协作Draft生成Prompts =========================

# Worker生成简洁draft的任务特定prompts
WORKER_DRAFT_PROMPTS = {
    "gsm8k": """Create a concise draft to solve this grade school math problem. 
Work step by step, keep each step brief and clear. Focus on correct arithmetic.

Format:
- Step 1: [brief reasoning]
- Step 2: [brief reasoning]
- Final: [answer]
#### [number]""",

    "math": """Create a concise draft to solve this advanced math problem.
Apply mathematical reasoning systematically, keep steps concise but rigorous.

Format:
- Method: [approach]
- Step 1: [key calculation]
- Step 2: [key calculation]
- Final: [answer]
#### [answer]""",

    "mbpp": """Create a concise draft for this Python coding problem.
Think through the requirements clearly, keep reasoning brief.

Format:
- Requirements: [what needs to be done]
- Logic: [key algorithm insight]
- Implementation: [approach]
#### [Python code starting with def]""",

    "humaneval": """Create a concise draft for this Python function completion.
Analyze requirements and implementation approach concisely.

Format:
- Function goal: [what it should do]
- Algorithm: [key approach]
- Edge cases: [important considerations]
#### [Python code starting with def]""",

    "drop": """Create a concise draft for this reading comprehension question.
Extract information systematically, keep reasoning brief.

Format:
- Key info: [relevant facts from passage]
- Calculation: [if needed]
- Answer: [factual response]
#### [answer]""",

    "hotpotqa": """Create a concise draft for this multi-hop reasoning question.
Connect information across sources step by step.

Format:
- Entity 1: [first key information]
- Entity 2: [second key information]
- Connection: [how they relate]
- Answer: [final result]
#### [answer]""",

    "strategyqa": """Create a concise draft for this yes/no question.
Consider implicit knowledge and real-world constraints.

Format:
- Key facts: [relevant information]
- Logic: [reasoning chain]
- Conclusion: [yes/no with brief reason]
#### [yes/no]""",

    "gpqa": """Create a concise draft for this graduate-level science question.
Apply scientific knowledge systematically.

Format:
- Domain: [scientific area]
- Key principle: [relevant theory/formula]
- Analysis: [evaluate options]
- Answer: [best choice]
#### [A/B/C/D]""",

    "mmlu": """Create a concise draft for this multiple-choice question.
Use comprehensive knowledge to evaluate options.

Format:
- Topic: [subject area]
- Key knowledge: [relevant facts]
- Eliminate: [wrong options]
- Answer: [correct choice]
#### [A/B/C/D]"""
}

# Worker协作讨论prompts
WORKER_COLLABORATION_PROMPTS = {
    "discuss": """You are participating in a multi-agent collaborative discussion to solve this problem.

PROBLEM: {question}
TASK TYPE: {task_type}

OTHER AGENTS' DRAFTS:
{other_drafts}

Your task: Create your own draft or improve existing ones. Focus on:
1. Accuracy and correctness
2. Clear reasoning steps
3. Proper format for the task type

{task_prompt}

Generate your collaborative draft:""",

    "improve": """You are improving a collaborative draft based on discussion.

PROBLEM: {question}
TASK TYPE: {task_type}

CURRENT DRAFT:
{current_draft}

FEEDBACK/SUGGESTIONS:
{feedback}

Your task: Improve the draft based on feedback. Keep it concise but accurate.

{task_prompt}

Generate your improved draft:""",

    "validate": """You are validating a collaborative draft for accuracy.

PROBLEM: {question}
TASK TYPE: {task_type}

DRAFT TO VALIDATE:
{draft}

Your task: Check for errors and suggest corrections. Focus on:
1. Logical correctness
2. Calculation accuracy
3. Proper format

Provide validation feedback:
- Issues found: [list any problems]
- Corrections needed: [specific fixes]
- Overall quality: [good/needs improvement]"""
}

# ========================= Merger Agent Prompts =========================

MERGER_PROMPTS = {
    "merge_annotations": """You are a merge agent combining multiple draft annotations into a coherent final draft.

ORIGINAL PROBLEM: {question}
TASK TYPE: {task_type}

WORKER DRAFTS WITH ANNOTATIONS:
{annotated_drafts}

Your task: Merge the best insights from all drafts into ONE final, concise draft.

Guidelines:
1. Keep the strongest reasoning from each draft
2. Resolve any conflicts logically
3. Ensure the final answer is correct
4. Maintain the concise format

{task_prompt}

Generate the merged final draft:""",

    "resolve_conflicts": """You are resolving conflicts between different draft approaches.

PROBLEM: {question}
TASK TYPE: {task_type}

CONFLICTING DRAFTS:
{conflicting_drafts}

Your task: Determine which approach is most correct and create a unified draft.

Focus on:
1. Logical soundness
2. Calculation accuracy
3. Proper methodology

{task_prompt}

Generate the resolved draft:""",

    "synthesize": """You are synthesizing multiple draft insights into a final answer.

PROBLEM: {question}
TASK TYPE: {task_type}

DRAFT INSIGHTS:
{insights}

Your task: Combine all valid insights into ONE clear, concise final draft.

{task_prompt}

Generate the synthesized draft:"""
}

# ========================= Leader Evaluation Prompts =========================

LEADER_EVALUATION_PROMPTS = {
    "evaluate_quality": """You are a leader agent evaluating the quality of a collaborative draft.

PROBLEM: {question}
TASK TYPE: {task_type}

DRAFT TO EVALUATE:
{draft}

Evaluate this draft on:
1. Accuracy (0.0-1.0)
2. Completeness (0.0-1.0)
3. Clarity (0.0-1.0)
4. Format correctness (0.0-1.0)

Provide assessment with structured feedback:
```json
{{
  "overall_score": 0.0-1.0,
  "accuracy_score": 0.0-1.0,
  "completeness_score": 0.0-1.0,
  "clarity_score": 0.0-1.0,
  "format_score": 0.0-1.0,
  "decision": "approved|needs_minor_revision|needs_major_revision|rejected",
  "feedback": "general feedback for improvement",
  "feedback_annotations": [
    {{
      "span_id": 1,
      "issue_type": "logical_gap|calculation_error|format_issue|clarity_issue",
      "comment": "specific issue description",
      "severity": "high|medium|low"
    }}
  ]
}}
```

For feedback_annotations:
- Use span_id to identify specific parts (1=beginning, 2=middle, 3=end)
- issue_type should be specific and actionable
- comment should be concise but clear
- severity helps prioritize fixes

EVALUATION CRITERIA:
- Score below 0.7: needs_major_revision or rejected
- Score 0.7-0.85: needs_minor_revision
- Score above 0.85: approved

Be strict in evaluation to ensure high quality.""",

    "provide_feedback": """You are a leader agent providing feedback for draft improvement.

PROBLEM: {question}
TASK TYPE: {task_type}

CURRENT DRAFT:
{draft}

ISSUES IDENTIFIED:
{issues}

Provide specific improvement guidance:
1. What needs to be fixed?
2. How to fix it?
3. What approach should be taken?

Your feedback should help workers create a better draft in the next iteration.

Improvement guidance:""",

    "final_decision": """You are a leader agent making the final decision on a collaborative draft.

PROBLEM: {question}
TASK TYPE: {task_type}

FINAL DRAFT:
{final_draft}

COLLABORATION HISTORY:
{history}

Make your final decision:
1. Is this draft ready for submission?
2. Does it correctly answer the question?
3. Is the format appropriate?

Decision: [APPROVE/REJECT/REVISE]
Reason: [brief explanation]

If APPROVE, output the final answer in the required format.
If REJECT/REVISE, provide specific improvement instructions."""
}

# ========================= Task-specific Answer Formats =========================

EXPECTED_ANSWER_FORMATS = {
    "gsm8k": "#### [number]",
    "math": "#### [answer]", 
    "mbpp": "[raw Python code starting with def]",
    "humaneval": "[raw Python code starting with def]",
    "drop": "#### [answer]",
    "hotpotqa": "#### [answer]",
    "strategyqa": "#### [yes/no]",
    "gpqa": "#### [A/B/C/D]",
    "mmlu": "#### [A/B/C/D]"
}

# ========================= Few-shot Examples =========================

DRAFT_EXAMPLES = {
    "gsm8k": {
        "question": "Jason had 20 lollipops. He gave Denny some lollipops. Now Jason has 12 lollipops. How many lollipops did Jason give to Denny?",
        "draft": """- Start: Jason had 20 lollipops
- End: Jason has 12 lollipops  
- Given away: 20 - 12 = 8
#### 8"""
    },
    
    "math": {
        "question": "Find the derivative of f(x) = x^3 + 2x^2 - 5x + 3",
        "draft": """- Apply power rule: d/dx(x^n) = nx^(n-1)
- Term 1: 3x^2
- Term 2: 4x
- Term 3: -5
- Constant: 0
#### 3x^2 + 4x - 5"""
    },
    
    "strategyqa": {
        "question": "Could a goldfish survive in space?",
        "draft": """- Fish need: water, oxygen, pressure
- Space has: vacuum, no oxygen, no pressure
- Goldfish cannot survive these conditions
#### no"""
    }
}

# ========================= Helper Functions =========================

def get_worker_draft_prompt(task_type: str) -> str:
    """获取Worker生成draft的任务特定prompt"""
    return WORKER_DRAFT_PROMPTS.get(task_type, WORKER_DRAFT_PROMPTS["gsm8k"])

def get_worker_collaboration_prompt(prompt_type: str, **kwargs) -> str:
    """获取Worker协作prompt"""
    template = WORKER_COLLABORATION_PROMPTS.get(prompt_type, WORKER_COLLABORATION_PROMPTS["discuss"])
    return template.format(**kwargs)

def get_merger_prompt(prompt_type: str, **kwargs) -> str:
    """获取Merger合并prompt"""
    template = MERGER_PROMPTS.get(prompt_type, MERGER_PROMPTS["merge_annotations"])
    return template.format(**kwargs)

def get_leader_evaluation_prompt(prompt_type: str, **kwargs) -> str:
    """获取Leader评估prompt"""
    template = LEADER_EVALUATION_PROMPTS.get(prompt_type, LEADER_EVALUATION_PROMPTS["evaluate_quality"])
    return template.format(**kwargs)

def get_system_prompt(task_type: str = "standard") -> str:
    """获取系统prompt"""
    base_prompt = SYSTEM_PROMPT
    if task_type in DATASET_SYSTEM_ENHANCEMENTS:
        base_prompt += f"\n\nFor this task: {DATASET_SYSTEM_ENHANCEMENTS[task_type]}"
    return base_prompt

def get_expected_format(task_type: str) -> str:
    """获取期望的答案格式"""
    return EXPECTED_ANSWER_FORMATS.get(task_type, "#### [answer]")

def get_draft_example(task_type: str) -> dict:
    """获取draft示例"""
    return DRAFT_EXAMPLES.get(task_type, DRAFT_EXAMPLES["gsm8k"])

def create_complete_worker_prompt(task_type: str, question: str, context: dict = None) -> str:
    """创建完整的Worker prompt，包含示例"""
    base_prompt = get_worker_draft_prompt(task_type)
    example = get_draft_example(task_type)
    
    prompt = f"""{base_prompt}

Example:
Question: {example['question']}
Draft: {example['draft']}

Now create your draft:
Question: {question}
Draft:"""
    
    return prompt

# ========================= Compatibility Functions =========================

class WorkerAnnotationPrompts:
    """兼容性类，保持现有接口"""
    
    @staticmethod
    def get_annotation_generation_prompt(context, strategy, max_annotations: int) -> str:
        """简化的批注生成prompt"""
        task_type = getattr(context, 'question_type', 'standard')
        question = getattr(context, 'question_content', '')
        draft = getattr(context, 'draft_content', '')
        
        return f"""You are collaborating on this problem. Analyze the current draft and provide brief, actionable suggestions.

PROBLEM: {question}
TASK TYPE: {task_type}
CURRENT DRAFT: {draft}

Provide up to {max_annotations} concise suggestions:
1. What should be improved?
2. How to fix any errors?
3. What's missing?

Focus on correctness and clarity. Keep suggestions brief and specific."""

class LeaderEvaluationPrompts:
    """兼容性类，保持现有接口"""
    
    @staticmethod
    def get_leader_evaluation_system_prompt() -> str:
        """Leader评估系统prompt"""
        return "You are a leader agent responsible for evaluating collaborative drafts and providing quality assessments."

class LeaderAgentPrompts:
    """Leader Agent Prompts for final answer generation and quality assessment"""

    @staticmethod
    def get_leader_synthesis_prompt() -> str:
        """Leader合成prompt"""
        return "You are a leader agent synthesizing collaborative results into a final answer."
    
    @staticmethod
    def get_synthesis_prompt(question_content: str, question_type: str, current_content: str) -> str:
        """Generate final answer from collaborative draft - optimized for HotpotQA"""
        
        # Special handling for HotpotQA to ensure short, precise answers
        if question_type == "hotpotqa":
            return f"""You are synthesizing a collaborative draft into a final answer for HotpotQA.

QUESTION: {question_content}

COLLABORATIVE DRAFT:
{current_content}

CRITICAL INSTRUCTIONS for HotpotQA - FOLLOW EXACTLY:
1. 输出格式必须是：ANSWER: [核心答案]
2. 核心答案必须是最简短的形式：
   - 人名：只要姓名，不要头衔或描述
   - 公司：只要核心名称或类型
   - 日期：只要具体日期
   - 地点：只要地名
3. 绝对不要包含：
   - 解释性文字
   - 推理过程
   - 编号或列表
   - 完整句子
   - 标点符号（除必要的）

Examples:
- For "What year was X born?" → ANSWER: 1995
- For "Who is the CEO of X?" → ANSWER: John Smith
- For "Which company owns X?" → ANSWER: Microsoft
- For "What type of company is AIA Group?" → ANSWER: life insurance group
- For "What TV show is X a prequel to?" → ANSWER: The Big Bang Theory

从上面的协作草稿中，只提取最核心的答案：

ANSWER:"""

    @staticmethod
    def get_enhanced_synthesis_prompt(question_content: str, question_type: str, current_content: str) -> str:
        """获取增强的synthesis prompt - 双通道输出，强制简洁答案"""

        # 通用的硬约束指令
        hard_constraints = """
CRITICAL INSTRUCTIONS - MUST FOLLOW EXACTLY:
1. 忽略草稿中的所有元信息（"Here's an improved draft", "Key improvements", "The draft now"等）
2. 只关注实际的事实内容和推理结果
3. 输出格式必须是：ANSWER: [最简答案]
4. 答案必须是一行，不超过20个词
5. 不要添加任何解释、推理过程或额外文字
6. 当且仅当你已确定最终答案时，输出ANSWER: <最简答案>，除此之外不要添加任何文字
"""

        # Special handling for HotpotQA
        if question_type == "hotpotqa":
            return f"""你正在为HotpotQA问题提取最终答案。

QUESTION: {question_content}

COLLABORATIVE DRAFT:
{current_content}

{hard_constraints}

HotpotQA特殊要求：
- 人名：只要姓名（如：John Smith）
- 公司类型：包含完整描述（如：pan-Asian life insurance group）
- 日期：只要具体日期（如：March 15, 1995）
- 地点：只要地名（如：New York）
- TV节目：只要节目名（如：The Big Bang Theory）
- 保持原文中的关键修饰词（如：largest, independent, pan-Asian等）

从草稿中提取核心答案：

ANSWER:"""

        # Special handling for Math problems
        elif question_type == "math":
            return f"""你正在为Math问题提取最终答案。

QUESTION: {question_content}

COLLABORATIVE DRAFT:
{current_content}

{hard_constraints}

Math特殊要求：
- 只输出数值，不包含单位、解释或推理过程
- 分数化简到最简形式
- 小数保留合理精度
- 不要包含"="号或其他符号

Examples:
- For "What is 2+3?" → ANSWER: 5
- For "Solve for x: 2x=10" → ANSWER: 5
- For "What is 1/2 + 1/4?" → ANSWER: 3/4

从草稿中提取数值答案：

ANSWER:"""

        # Special handling for GSM8K problems
        elif question_type == "gsm8k":
            return f"""你正在为GSM8K问题提取最终答案。

QUESTION: {question_content}

COLLABORATIVE DRAFT:
{current_content}

{hard_constraints}

GSM8K特殊要求：
- 寻找"#### 数字"格式的最终答案
- 只输出数字部分，不包含####符号
- 不要包含单位、解释或推理过程
- 整数答案不需要小数点

Examples:
- For "#### 15" → ANSWER: 15
- For "#### 25" → ANSWER: 25
- For "#### 31" → ANSWER: 31

从草稿中提取数值答案（特别注意####格式）：

ANSWER:"""

        # Special handling for coding problems
        elif question_type in ["mbpp", "humaneval"]:
            return f"""你正在为编程问题提取最终代码。

QUESTION: {question_content}

COLLABORATIVE DRAFT:
{current_content}

CRITICAL INSTRUCTIONS for Coding:
1. 输出完整的Python函数代码，不是单行表达式
2. 保持正确的缩进和格式
3. 包含函数定义和完整的函数体
4. 不要添加额外的解释或注释
5. 确保代码可以直接执行

Examples:
- For function definition → 输出完整函数
- For algorithm implementation → 输出完整实现

从草稿中提取完整代码：

```python
[完整函数代码]
```"""

        # For other question types, use enhanced synthesis
        return f"""你正在为{question_type}问题提取最终答案。

QUESTION: {question_content}

COLLABORATIVE DRAFT:
{current_content}

{hard_constraints}

通用要求：
- 人名：只要姓名
- 是非题：只要Yes或No
- 日期：只要具体日期
- 公司：只要核心名称

Examples:
- For person names → ANSWER: John Smith
- For yes/no questions → ANSWER: Yes
- For dates → ANSWER: March 15, 1995
- For companies → ANSWER: Microsoft Corporation

从草稿中提取核心答案：

ANSWER:"""

    @staticmethod  
    def get_direct_answer_prompt(question_content: str, question_type: str) -> str:
        """Generate answer directly from question when draft is empty"""
        
        # Special handling for HotpotQA
        if question_type == "hotpotqa":
            return f"""Answer this HotpotQA question directly and concisely.

QUESTION: {question_content}

CRITICAL INSTRUCTIONS:
1. 只输出最简短的答案本身（不加句号、解释或编号）
2. 短语式答案即可，不需要完整句子或解释
3. 输出格式：ANSWER: [简短答案]

ANSWER:"""

        return f"""Answer this {question_type} question directly.

QUESTION: {question_content}

Provide a clear, accurate answer:"""
    
    @staticmethod
    def get_assessment_prompt(question_content: str, question_type: str, current_content: str) -> str:
        """Assess the quality of the current draft"""
        return f"""Assess the quality of this collaborative draft for a {question_type} question.

QUESTION: {question_content}

DRAFT:
{current_content}

Evaluate on a scale of 0.0 to 1.0 based on:
1. Accuracy and correctness
2. Completeness of the answer
3. Clarity and coherence
4. Relevance to the question

Provide only a numerical score (e.g., 0.85):"""
    
    @staticmethod
    def get_quality_assessment_prompt(question_content: str, question_type: str, final_answer: str) -> str:
        """Assess the quality of the final generated answer"""
        return f"""Assess the quality of this final answer for a {question_type} question.

QUESTION: {question_content}

FINAL ANSWER: {final_answer}

Rate the answer quality on a scale of 0.0 to 1.0 considering:
1. Accuracy and correctness
2. Completeness
3. Clarity and format appropriateness
4. Direct relevance to the question

Provide only a numerical score (e.g., 0.92):"""

class ConsensusPrompts:
    """共识机制相关的Prompt类"""

    @staticmethod
    def get_consensus_synthesis_prompt(consensus_item) -> str:
        """获取共识综合prompt"""
        return f"""You are facilitating a consensus discussion among AI agents.

DISCUSSION TOPIC: {getattr(consensus_item, 'subject', 'General discussion')}
CURRENT STATUS: {getattr(consensus_item, 'status', 'in_discussion')}

Based on the discussion messages, synthesize a consensus view that:
1. Addresses the main points of agreement
2. Acknowledges areas of disagreement
3. Provides a balanced synthesis

Keep the synthesis concise and actionable."""

    @staticmethod
    def get_consensus_moderator_system_prompt() -> str:
        """获取共识调节者系统prompt"""
        return """You are a consensus moderator for AI agent discussions. Your role is to:
- Facilitate productive discussions
- Identify areas of agreement and disagreement
- Synthesize different viewpoints into coherent consensus
- Maintain objectivity and balance
- Focus on actionable outcomes"""

    @staticmethod
    def get_agent_discussion_prompt(agent_id: str, discussion_context: str) -> str:
        """获取智能体讨论prompt"""
        return f"""You are agent {agent_id} participating in a collaborative discussion.

DISCUSSION CONTEXT: {discussion_context}

Provide your perspective on this topic. Be:
- Constructive and collaborative
- Specific and evidence-based
- Open to other viewpoints
- Focused on finding solutions

Your response should be concise but substantive."""

class MergeAgentPrompts:
    """合并代理相关的Prompt类"""

    @staticmethod
    def get_sequential_merge_prompt() -> str:
        """获取顺序合并prompt"""
        return """You are merging annotations sequentially. Integrate each suggestion in order while maintaining coherence."""

    @staticmethod
    def get_semantic_synthesis_prompt() -> str:
        """获取语义合成prompt"""
        return """You are performing semantic synthesis. Intelligently combine related suggestions and create a coherent unified response."""

    @staticmethod
    def get_conflict_resolution_prompt() -> str:
        """获取冲突解决prompt"""
        return """You are resolving conflicts between annotations. Identify contradictions and provide a balanced resolution."""

    @staticmethod
    def get_priority_based_prompt() -> str:
        """获取基于优先级的prompt"""
        return """You are merging based on priority. Focus on the most important suggestions while incorporating others where appropriate."""

    @staticmethod
    def get_creative_combination_prompt() -> str:
        """获取创造性组合prompt"""
        return """You are creatively combining diverse perspectives. Synthesize different viewpoints into an innovative solution."""

    @staticmethod
    def create_full_merge_prompt(strategy_prompt: str, current_draft: str,
                               annotations_text: str, analysis_text: str, conflicts_text: str) -> str:
        """创建完整的合并prompt"""
        return f"""You are a merge agent combining multiple annotations into a coherent draft.

STRATEGY: {strategy_prompt}

CURRENT DRAFT:
{current_draft}

ANNOTATIONS TO MERGE:
{annotations_text}

ANALYSIS:
{analysis_text}

CONFLICTS:
{conflicts_text}

Your task: Create an improved draft that incorporates the valuable suggestions while resolving any conflicts. Maintain the original intent while improving quality."""

# ========================= 主要导出接口 =========================

def get_optimized_prompt(agent_type: str, task_type: str, **kwargs) -> str:
    """获取优化后的prompt"""
    if agent_type == "worker":
        return create_complete_worker_prompt(task_type, kwargs.get('question', ''), kwargs.get('context'))
    elif agent_type == "merger":
        return get_merger_prompt(kwargs.get('prompt_type', 'merge_annotations'), **kwargs)
    elif agent_type == "leader":
        return get_leader_evaluation_prompt(kwargs.get('prompt_type', 'evaluate_quality'), **kwargs)
    else:
        return get_system_prompt(task_type)