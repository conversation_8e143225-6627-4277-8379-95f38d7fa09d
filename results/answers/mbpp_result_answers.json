{"metadata": {"timestamp": "2025-07-23T05:26:57.821881", "models_used": ["openai", "anthropic", "llama"], "task_type": "mbpp"}, "results": [{"id": "db7d412d", "prompt": "Write a function to convert a given tuple of positive integers into a single integer.\n\ndef tuple_to_int(nums):", "solution": "def tuple_to_int(nums):\n    if not all(isinstance(x, int) and x > 0 for x in nums):\n        raise ValueError(\"Tuple must contain only positive integers.\")\n    return int(''.join(map(str, nums)))", "execution_time": 32.55085110664368, "code": "def tuple_to_int(nums):\n    if not all(isinstance(x, int) and x > 0 for x in nums):\n        raise ValueError(\"Tuple must contain only positive integers.\")\n    return int(''.join(map(str, nums)))", "problem_index": 0, "quality_score": 0.95, "processing_mode": "simplified_collaborative"}, {"id": "308928d6", "prompt": "Write a function that takes in two numbers and returns a tuple with the second number and then the first number.\n\ndef swap_numbers(a,b):", "solution": "def swap_numbers(a: int | float, b: int | float) -> tuple[int | float, int | float]:\n    \"\"\"\n    Swap the order of two input numbers.\n\n    Args:\n        a (int | float): The first input number.\n        b (int | float): The second input number.\n\n    Returns:\n        tuple[int | float, int | float]: A tuple with the numbers in swapped order (b, a).\n\n    Examples:\n        >>> swap_numbers(5, 10)\n        (10, 5)\n        >>> swap_numbers(3.14, 2.0)\n        (2.0, 3.14)\n    \"\"\"\n    return (b, a)", "execution_time": 83.64027309417725, "code": "def swap_numbers(a: int | float, b: int | float) -> tuple[int | float, int | float]:\n    \"\"\"\n    Swap the order of two input numbers.\n\n    Args:\n        a (int | float): The first input number.\n        b (int | float): The second input number.\n\n    Returns:\n        tuple[int | float, int | float]: A tuple with the numbers in swapped order (b, a).\n\n    Examples:\n        >>> swap_numbers(5, 10)\n        (10, 5)\n        >>> swap_numbers(3.14, 2.0)\n        (2.0, 3.14)\n    \"\"\"\n    return (b, a)", "problem_index": 1, "quality_score": 0.95, "processing_mode": "simplified_collaborative"}, {"id": "a696ab6a", "prompt": "Write a python function to find the last digit of a given number.\n\ndef last_Digit(n) :", "solution": "def last_Digit(n: int) -> int:\n    \"\"\"\n    Returns the last digit of a given integer.\n\n    Parameters:\n    n (int): The input integer.\n\n    Returns:\n    int: The last digit of the input integer.\n    \"\"\"\n    # The modulo operation with 10 isolates the last digit by returning the remainder of division.\n    return abs(n) % 10\n", "execution_time": 76.5959620475769, "code": "def last_Digit(n: int) -> int:\n    \"\"\"\n    Returns the last digit of a given integer.\n\n    Parameters:\n    n (int): The input integer.\n\n    Returns:\n    int: The last digit of the input integer.\n    \"\"\"\n    # The modulo operation with 10 isolates the last digit by returning the remainder of division.\n    return abs(n) % 10", "problem_index": 2, "quality_score": 0.9, "processing_mode": "simplified_collaborative"}, {"id": "e4887640", "prompt": "Write a function to check whether it follows the sequence given in the patterns array.\n\ndef is_samepatterns(colors, patterns):    ", "solution": "def matches_pattern(colors: List[Any], patterns: List[Any]) -> bool:\n    \"\"\"\n    Checks whether the given sequence of colors follows the specified patterns.\n\n    Args:\n    colors (List[Any]): A list of colors.\n    patterns (List[Any]): A list of patterns.\n\n    Returns:\n    bool: True if the color sequence matches the patterns, False otherwise.\n\n    Example:\n    >>> matches_pattern(['red', 'blue', 'red'], ['A', 'B', 'A'])\n    True\n    >>> matches_pattern(['red', 'blue', 'green'], ['A', 'B', 'A'])\n    False\n    \"\"\"\n    if not isinstance(colors, list) or not isinstance(patterns, list):\n        raise TypeError(\"Both colors and patterns must be lists.\")\n    if len(colors) != len(patterns):\n        return False\n    if not colors and not patterns:\n        return True  # Both are empty lists\n\n    color_to_pattern = {}\n    pattern_to_color = {}\n    \n    for color, pattern in zip(colors, patterns):\n        if color in color_to_pattern:\n            if color_to_pattern[color] != pattern:\n                return False\n        else:\n            color_to_pattern[color] = pattern\n        \n        if pattern in pattern_to_color:\n            if pattern_to_color[pattern] != color:\n                return False\n        else:\n            pattern_to_color[pattern] = color\n            \n    return True", "execution_time": 92.75113487243652, "code": "def matches_pattern(colors: List[Any], patterns: List[Any]) -> bool:\n    \"\"\"\n    Checks whether the given sequence of colors follows the specified patterns.\n\n    Args:\n    colors (List[Any]): A list of colors.\n    patterns (List[Any]): A list of patterns.\n\n    Returns:\n    bool: True if the color sequence matches the patterns, False otherwise.\n\n    Example:\n    >>> matches_pattern(['red', 'blue', 'red'], ['A', 'B', 'A'])\n    True\n    >>> matches_pattern(['red', 'blue', 'green'], ['A', 'B', 'A'])\n    False\n    \"\"\"\n    if not isinstance(colors, list) or not isinstance(patterns, list):\n        raise TypeError(\"Both colors and patterns must be lists.\")\n    if len(colors) != len(patterns):\n        return False\n    if not colors and not patterns:\n        return True  # Both are empty lists\n\n    color_to_pattern = {}\n    pattern_to_color = {}\n    \n    for color, pattern in zip(colors, patterns):\n        if color in color_to_pattern:\n            if color_to_pattern[color] != pattern:\n                return False\n        else:\n            color_to_pattern[color] = pattern\n        \n        if pattern in pattern_to_color:\n            if pattern_to_color[pattern] != color:\n                return False\n        else:\n            pattern_to_color[pattern] = color\n            \n    return True", "problem_index": 3, "quality_score": 0.9, "processing_mode": "simplified_collaborative"}, {"id": "c0f57d53", "prompt": "Write a python function to check whether the given number can be represented as sum of non-zero powers of 2 or not.\n\ndef is_Sum_Of_Powers_Of_Two(n): ", "solution": "#### def is_Sum_Of_Powers_Of_Two(n):\n        return False\n    return (n & (n >> 1)) == 0", "execution_time": 96.7220869064331, "code": "def is_Sum_Of_Powers_Of_Two(n):\n        return False\n    return (n & (n >> 1)) == 0", "problem_index": 4, "quality_score": 0.7, "processing_mode": "simplified_collaborative"}, {"id": "7c5f3e96", "prompt": "Write a python function to find sum of products of all possible sublists of a given list. https://www.geeksforgeeks.org/sum-of-products-of-all-possible-subarrays/\n\ndef sum_Of_Subarray_Prod(arr):", "solution": "#### def sum_Of_Subarray_Prod(arr):\n    return total_sum", "execution_time": 117.81117010116577, "code": "def sum_Of_Subarray_Prod(arr):\n    return total_sum", "problem_index": 5, "quality_score": 0.9, "processing_mode": "simplified_collaborative"}, {"id": "7d98aed4", "prompt": "Write a function to calculate the maximum aggregate from the list of tuples.\nfrom collections import defaultdict\ndef max_aggregate(stdata):", "solution": "def max_aggregate(stdata: List[Tuple[Hashable, int]]) -> int:\n    \"\"\"\n    Calculate the maximum aggregate value from a list of (key, value) tuples.\n\n    Args:\n        stdata (List[Tuple[Hashable, int]]): A list of tuples where each tuple contains a key and a value.\n\n    Returns:\n        int: The maximum aggregate value or 0 if the input list is empty.\n    \"\"\"\n    if not stdata:\n        return 0\n\n    aggregates = defaultdict(int)\n    for key, value in stdata:\n        aggregates[key] += value\n\n    return max(aggregates.values())", "execution_time": 91.47681093215942, "code": "def max_aggregate(stdata: List[Tuple[Hashable, int]]) -> int:\n    \"\"\"\n    Calculate the maximum aggregate value from a list of (key, value) tuples.\n\n    Args:\n        stdata (List[Tuple[Hashable, int]]): A list of tuples where each tuple contains a key and a value.\n\n    Returns:\n        int: The maximum aggregate value or 0 if the input list is empty.\n    \"\"\"\n    if not stdata:\n        return 0\n\n    aggregates = defaultdict(int)\n    for key, value in stdata:\n        aggregates[key] += value\n\n    return max(aggregates.values())", "problem_index": 6, "quality_score": 0.92, "processing_mode": "simplified_collaborative"}]}