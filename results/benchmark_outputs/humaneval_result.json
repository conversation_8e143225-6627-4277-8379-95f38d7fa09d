{"metadata": {"timestamp": "2025-07-23T05:26:57.820910", "models_used": ["openai", "anthropic", "llama"], "task_type": "humaneval"}, "results": [{"id": "62f9b2a2", "prompt": "\ndef solve(N):\n    \"\"\"Given a positive integer N, return the total sum of its digits in binary.\n    \n    Example\n        For N = 1000, the sum of digits will be 1 the output should be \"1\".\n        For N = 150, the sum of digits will be 6 the output should be \"110\".\n        For N = 147, the sum of digits will be 12 the output should be \"1100\".\n    \n    Variables:\n        @N integer\n             Constraints: 0 ≤ N ≤ 10000.\n    Output:\n         a string of binary number\n    \"\"\"\n", "solution": "def solve(N):\n    \"\"\"Given a positive integer N, return the total sum of its digits in binary.\n    \n    Example\n        For N = 1000, the sum of digits will be 1 the output should be \"1\".\n        For N = 150, the sum of digits will be 6 the output should be \"110\".\n        For N = 147, the sum of digits will be 12 the output should be \"1100\".\n    \n    Variables:\n        @N integer\n             Constraints: 0 ≤ N ≤ 10000.\n    Output:\n         a string of binary number\n    \"\"\"\n    if not isinstance(N, int) or N < 0 or N > 10000:\n        raise ValueError(\"N must be a non-negative integer between 0 and 10000.\")\n    digit_sum = sum(int(digit) for digit in str(N))\n    return bin(digit_sum)[2:]", "execution_time": 40.16825890541077, "code": "def solve(N):\n    \"\"\"Given a positive integer N, return the total sum of its digits in binary.\n    \n    Example\n        For N = 1000, the sum of digits will be 1 the output should be \"1\".\n        For N = 150, the sum of digits will be 6 the output should be \"110\".\n        For N = 147, the sum of digits will be 12 the output should be \"1100\".\n    \n    Variables:\n        @N integer\n             Constraints: 0 ≤ N ≤ 10000.\n    Output:\n         a string of binary number\n    \"\"\"\n    if not isinstance(N, int) or N < 0 or N > 10000:\n        raise ValueError(\"N must be a non-negative integer between 0 and 10000.\")\n    digit_sum = sum(int(digit) for digit in str(N))\n    return bin(digit_sum)[2:]", "problem_index": 0, "quality_score": 0.95, "processing_mode": "simplified_collaborative"}, {"id": "b39fa437", "prompt": "\n\ndef truncate_number(number: float) -> float:\n    \"\"\" Given a positive floating point number, it can be decomposed into\n    and integer part (largest integer smaller than given number) and decimals\n    (leftover part always smaller than 1).\n\n    Return the decimal part of the number.\n    >>> truncate_number(3.5)\n    0.5\n    \"\"\"\n", "solution": "def truncate_number(number: float) -> float:\n    if number < 0:\n        raise ValueError(\"Input must be a positive floating point number.\")\n    integer_part = int(number)\n    decimal_part = number - integer_part\n    return decimal_part", "execution_time": 37.86823296546936, "code": "def truncate_number(number: float) -> float:\n    if number < 0:\n        raise ValueError(\"Input must be a positive floating point number.\")\n    integer_part = int(number)\n    decimal_part = number - integer_part\n    return decimal_part", "problem_index": 1, "quality_score": 0.95, "processing_mode": "simplified_collaborative"}, {"id": "819e4285", "prompt": "\n\ndef skjkasdkd(lst):\n    \"\"\"You are given a list of integers.\n    You need to find the largest prime value and return the sum of its digits.\n\n    Examples:\n    For lst = [0,3,2,1,3,5,7,4,5,5,5,2,181,32,4,32,3,2,32,324,4,3] the output should be 10\n    For lst = [1,0,1,8,2,4597,2,1,3,40,1,2,1,2,4,2,5,1] the output should be 25\n    For lst = [1,3,1,32,5107,34,83278,109,163,23,2323,32,30,1,9,3] the output should be 13\n    For lst = [0,724,32,71,99,32,6,0,5,91,83,0,5,6] the output should be 11\n    For lst = [0,81,12,3,1,21] the output should be 3\n    For lst = [0,8,1,2,1,7] the output should be 7\n    \"\"\"\n", "solution": "def skjkasdkd(lst):\n    def is_prime(n):\n        if n <= 1:\n            return False\n        if n == 2:\n            return True\n        if n % 2 == 0:\n            return False\n        max_divisor = int(n**0.5) + 1\n        for d in range(3, max_divisor, 2):\n            if n % d == 0:\n                return False\n        return True\n    \n    max_prime = 0\n    for num in lst:\n        if is_prime(num) and num > max_prime:\n            max_prime = num\n    \n    if max_prime == 0:\n        return 0\n    else:\n        return sum(int(digit) for digit in str(max_prime))", "execution_time": 38.97073173522949, "code": "def skjkasdkd(lst):\n    def is_prime(n):\n        if n <= 1:\n            return False\n        if n == 2:\n            return True\n        if n % 2 == 0:\n            return False\n        max_divisor = int(n**0.5) + 1\n        for d in range(3, max_divisor, 2):\n            if n % d == 0:\n                return False\n        return True\n    \n    max_prime = 0\n    for num in lst:\n        if is_prime(num) and num > max_prime:\n            max_prime = num\n    \n    if max_prime == 0:\n        return 0\n    else:\n        return sum(int(digit) for digit in str(max_prime))", "problem_index": 2, "quality_score": 0.95, "processing_mode": "simplified_collaborative"}, {"id": "e66405c4", "prompt": "\n\ndef triangle_area(a, h):\n    \"\"\"Given length of a side and high return area for a triangle.\n    >>> triangle_area(5, 3)\n    7.5\n    \"\"\"\n", "solution": "def triangle_area(a, h):\n    \"\"\"Given length of a side and high return area for a triangle.\n    >>> triangle_area(5, 3)\n    7.5\n    \"\"\"\n    if not isinstance(a, (int, float)) or not isinstance(h, (int, float)):\n        raise TypeError(\"Base and height must be numeric\")\n    if a < 0 or h < 0:\n        raise ValueError(\"Base and height must be non-negative\")\n    return 0.5 * a * h", "execution_time": 30.19112992286682, "code": "def triangle_area(a, h):\n    \"\"\"Given length of a side and high return area for a triangle.\n    >>> triangle_area(5, 3)\n    7.5\n    \"\"\"\n    if not isinstance(a, (int, float)) or not isinstance(h, (int, float)):\n        raise TypeError(\"Base and height must be numeric\")\n    if a < 0 or h < 0:\n        raise ValueError(\"Base and height must be non-negative\")\n    return 0.5 * a * h", "problem_index": 3, "quality_score": 0.97, "processing_mode": "simplified_collaborative"}, {"id": "e0cc32e0", "prompt": "\n\ndef incr_list(l: list):\n    \"\"\"Return list with elements incremented by 1.\n    >>> incr_list([1, 2, 3])\n    [2, 3, 4]\n    >>> incr_list([5, 3, 5, 2, 3, 3, 9, 0, 123])\n    [6, 4, 6, 3, 4, 4, 10, 1, 124]\n    \"\"\"\n", "solution": "def incr_list(l: list):\n    return [x + 1 for x in l]", "execution_time": 33.15664196014404, "code": "def incr_list(l: list):\n    return [x + 1 for x in l]", "problem_index": 4, "quality_score": 0.95, "processing_mode": "simplified_collaborative"}, {"id": "97a074c8", "prompt": "\ndef search(lst):\n    '''\n    You are given a non-empty list of positive integers. Return the greatest integer that is greater than \n    zero, and has a frequency greater than or equal to the value of the integer itself. \n    The frequency of an integer is the number of times it appears in the list.\n    If no such a value exist, return -1.\n    Examples:\n        search([4, 1, 2, 2, 3, 1]) == 2\n        search([1, 2, 2, 3, 3, 3, 4, 4, 4]) == 3\n        search([5, 5, 4, 4, 4]) == -1\n    '''\n", "solution": "def search(lst):\n    '''\n    You are given a non-empty list of positive integers. Return the greatest integer that is greater than \n    zero, and has a frequency greater than or equal to the value of the integer itself. \n    The frequency of an integer is the number of times it appears in the list.\n    If no such a value exist, return -1.\n    \n    Edge cases:\n    - The list must be non-empty.\n    - The list must contain only positive integers.\n    - If all integers have frequencies less than their values, return -1.\n    \n    Examples:\n        search([4, 1, 2, 2, 3, 1]) == 2\n        search([1, 2, 2, 3, 3, 3, 4, 4, 4]) == 3\n        search([5, 5, 4, 4, 4]) == -1\n    '''\n    from collections import Counter\n\n    if not lst:\n        raise ValueError(\"List must be non-empty.\")\n    for num in lst:\n        if not isinstance(num, int) or num <= 0:\n            raise ValueError(\"List must contain only positive integers.\")\n    \n    frequency = Counter(lst)\n    \n    max_integer = -1\n    \n    for num, freq in frequency.items():\n        if freq >= num:\n            max_integer = max(max_integer, num) # Track the maximum integer that meets the condition\n    \n    return max_integer", "execution_time": 88.68057680130005, "code": "def search(lst):\n    '''\n    You are given a non-empty list of positive integers. Return the greatest integer that is greater than \n    zero, and has a frequency greater than or equal to the value of the integer itself. \n    The frequency of an integer is the number of times it appears in the list.\n    If no such a value exist, return -1.\n    \n    Edge cases:\n    - The list must be non-empty.\n    - The list must contain only positive integers.\n    - If all integers have frequencies less than their values, return -1.\n    \n    Examples:\n        search([4, 1, 2, 2, 3, 1]) == 2\n        search([1, 2, 2, 3, 3, 3, 4, 4, 4]) == 3\n        search([5, 5, 4, 4, 4]) == -1\n    '''\n    from collections import Counter\n\n    if not lst:\n        raise ValueError(\"List must be non-empty.\")\n    for num in lst:\n        if not isinstance(num, int) or num <= 0:\n            raise ValueError(\"List must contain only positive integers.\")\n    \n    frequency = Counter(lst)\n    \n    max_integer = -1\n    \n    for num, freq in frequency.items():\n        if freq >= num:\n            max_integer = max(max_integer, num) # Track the maximum integer that meets the condition\n    \n    return max_integer", "problem_index": 5, "quality_score": 0.9, "processing_mode": "simplified_collaborative"}, {"id": "c099a008", "prompt": "\ndef Strongest_Extension(class_name, extensions):\n    \"\"\"You will be given the name of a class (a string) and a list of extensions.\n    The extensions are to be used to load additional classes to the class. The\n    strength of the extension is as follows: Let CAP be the number of the uppercase\n    letters in the extension's name, and let SM be the number of lowercase letters \n    in the extension's name, the strength is given by the fraction CAP - SM. \n    You should find the strongest extension and return a string in this \n    format: ClassName.StrongestExtensionName.\n    If there are two or more extensions with the same strength, you should\n    choose the one that comes first in the list.\n    For example, if you are given \"Slices\" as the class and a list of the\n    extensions: ['SErviNGSliCes', 'Cheese', 'StuFfed'] then you should\n    return 'Slices.SErviNGSliCes' since 'SErviNGSliCes' is the strongest extension \n    (its strength is -1).\n    Example:\n    for Strongest_Extension('my_class', ['AA', 'Be', 'CC']) == 'my_class.AA'\n    \"\"\"\n", "solution": "def strongest_extension(class_name, extensions):\n    \"\"\"\n    Determine the strongest extension based on the strength criteria (CAP - SM) \n    and return the formatted string with the class name and strongest extension.\n    \n    The strength of an extension is calculated as the difference between the number \n    of uppercase letters (CAP) and the number of lowercase letters (SM) in its name. \n    If multiple extensions have the same strength, the first one encountered in the list is selected.\n    \n    Args:\n        class_name (str): The name of the class.\n        extensions (list): A list of extension names.\n    \n    Returns:\n        str: A string in the format 'ClassName.StrongestExtensionName' or \n             'ClassName.NoExtensionsProvided' if the extensions list is empty.\n    \"\"\"\n    if not extensions:\n        return f\"{class_name}.NoExtensionsProvided\"\n\n    strongest = None\n    max_strength = float('-inf')\n\n    for extension in extensions:\n        cap = sum(1 for char in extension if char.isupper())\n        sm = sum(1 for char in extension if char.islower())\n        strength = cap - sm\n\n        if strength > max_strength:\n            max_strength = strength\n            strongest = extension\n\n    return f\"{class_name}.{strongest}\"", "execution_time": 92.8153977394104, "code": "def strongest_extension(class_name, extensions):\n    \"\"\"\n    Determine the strongest extension based on the strength criteria (CAP - SM) \n    and return the formatted string with the class name and strongest extension.\n    \n    The strength of an extension is calculated as the difference between the number \n    of uppercase letters (CAP) and the number of lowercase letters (SM) in its name. \n    If multiple extensions have the same strength, the first one encountered in the list is selected.\n    \n    Args:\n        class_name (str): The name of the class.\n        extensions (list): A list of extension names.\n    \n    Returns:\n        str: A string in the format 'ClassName.StrongestExtensionName' or \n             'ClassName.NoExtensionsProvided' if the extensions list is empty.\n    \"\"\"\n    if not extensions:\n        return f\"{class_name}.NoExtensionsProvided\"\n\n    strongest = None\n    max_strength = float('-inf')\n\n    for extension in extensions:\n        cap = sum(1 for char in extension if char.isupper())\n        sm = sum(1 for char in extension if char.islower())\n        strength = cap - sm\n\n        if strength > max_strength:\n            max_strength = strength\n            strongest = extension\n\n    return f\"{class_name}.{strongest}\"", "problem_index": 6, "quality_score": 0.85, "processing_mode": "simplified_collaborative"}, {"id": "90a10f2b", "prompt": "from typing import List\n\n\ndef remove_duplicates(numbers: List[int]) -> List[int]:\n    \"\"\" From a list of integers, remove all elements that occur more than once.\n    Keep order of elements left the same as in the input.\n    >>> remove_duplicates([1, 2, 3, 2, 4])\n    [1, 3, 4]\n    \"\"\"\n", "solution": "def remove_duplicates(numbers: List[int]) -> List[int]:\n    \"\"\"Remove all elements that occur more than once from a list of integers.\n    Keep order of elements left the same as in the input.\n    Returns: A list of integers that occur exactly once, in original order.\n    \n    >>> remove_duplicates([1, 2, 3, 2, 4])\n    [1, 3, 4]\n    >>> remove_duplicates([])\n    []\n    >>> remove_duplicates([1, 1, 1])\n    []\n    >>> remove_duplicates([1, 2, 3])\n    [1, 2, 3]\n    >>> remove_duplicates([2,1,2,1])\n    []\n    \"\"\"\n    if numbers is None:\n        raise TypeError(\"Input cannot be None.\")\n    if not isinstance(numbers, list):\n        raise TypeError(\"Input must be a list.\")\n    \n    if not all(isinstance(num, int) for num in numbers):\n        raise ValueError(\"Input list must contain only integers.\")\n    \n    count = {}\n    for number in numbers:\n        count[number] = count.get(number, 0) + 1\n    \n    return [number for number in numbers if count[number] == 1]", "execution_time": 105.2583019733429, "code": "def remove_duplicates(numbers: List[int]) -> List[int]:\n    \"\"\"Remove all elements that occur more than once from a list of integers.\n    Keep order of elements left the same as in the input.\n    Returns: A list of integers that occur exactly once, in original order.\n    \n    >>> remove_duplicates([1, 2, 3, 2, 4])\n    [1, 3, 4]\n    >>> remove_duplicates([])\n    []\n    >>> remove_duplicates([1, 1, 1])\n    []\n    >>> remove_duplicates([1, 2, 3])\n    [1, 2, 3]\n    >>> remove_duplicates([2,1,2,1])\n    []\n    \"\"\"\n    if numbers is None:\n        raise TypeError(\"Input cannot be None.\")\n    if not isinstance(numbers, list):\n        raise TypeError(\"Input must be a list.\")\n    \n    if not all(isinstance(num, int) for num in numbers):\n        raise ValueError(\"Input list must contain only integers.\")\n    \n    count = {}\n    for number in numbers:\n        count[number] = count.get(number, 0) + 1\n    \n    return [number for number in numbers if count[number] == 1]", "problem_index": 7, "quality_score": 0.9, "processing_mode": "simplified_collaborative"}, {"id": "b4cd4aa3", "prompt": "\ndef file_name_check(file_name):\n    \"\"\"Create a function which takes a string representing a file's name, and returns\n    'Yes' if the the file's name is valid, and returns 'No' otherwise.\n    A file's name is considered to be valid if and only if all the following conditions \n    are met:\n    - There should not be more than three digits ('0'-'9') in the file's name.\n    - The file's name contains exactly one dot '.'\n    - The substring before the dot should not be empty, and it starts with a letter from \n    the latin alphapet ('a'-'z' and 'A'-'Z').\n    - The substring after the dot should be one of these: ['txt', 'exe', 'dll']\n    Examples:\n    file_name_check(\"example.txt\") # => 'Yes'\n    file_name_check(\"1example.dll\") # => 'No' (the name should start with a latin alphapet letter)\n    \"\"\"\n", "solution": "def file_name_check(file_name):\n    \"\"\"Create a function which takes a string representing a file's name, and returns\n    'Yes' if the file's name is valid, and returns 'No' otherwise.\n    A file's name is considered to be valid if and only if all the following conditions \n    are met:\n    - There should not be more than three digits ('0'-'9') in the file's name.\n    - The file's name contains exactly one dot '.'.\n    - The substring before the dot should not be empty, and it starts with a letter from \n    the latin alphabet ('a'-'z' and 'A'-Z').\n    - The substring after the dot should be one of these: ['txt', 'exe', 'dll'] (case-insensitive).\n    Edge cases include file names with no dots, multiple dots, a base name not starting with a letter, \n    more than three digits, and disallowed extensions. The extension check is case-insensitive.\n    \"\"\"\n    if file_name.count('.') != 1:\n        return 'No'\n    \n    base_name, extension = file_name.split('.')\n    \n    if not base_name[0].isalpha():\n        return 'No'\n    \n    if sum(c.isdigit() for c in file_name) > 3:\n        return 'No'\n    \n    if extension.lower() not in ['txt', 'exe', 'dll']:\n        return 'No'\n    \n    return 'Yes'", "execution_time": 83.49643993377686, "code": "def file_name_check(file_name):\n    \"\"\"Create a function which takes a string representing a file's name, and returns\n    'Yes' if the file's name is valid, and returns 'No' otherwise.\n    A file's name is considered to be valid if and only if all the following conditions \n    are met:\n    - There should not be more than three digits ('0'-'9') in the file's name.\n    - The file's name contains exactly one dot '.'.\n    - The substring before the dot should not be empty, and it starts with a letter from \n    the latin alphabet ('a'-'z' and 'A'-Z').\n    - The substring after the dot should be one of these: ['txt', 'exe', 'dll'] (case-insensitive).\n    Edge cases include file names with no dots, multiple dots, a base name not starting with a letter, \n    more than three digits, and disallowed extensions. The extension check is case-insensitive.\n    \"\"\"\n    if file_name.count('.') != 1:\n        return 'No'\n    \n    base_name, extension = file_name.split('.')\n    \n    if not base_name[0].isalpha():\n        return 'No'\n    \n    if sum(c.isdigit() for c in file_name) > 3:\n        return 'No'\n    \n    if extension.lower() not in ['txt', 'exe', 'dll']:\n        return 'No'\n    \n    return 'Yes'", "problem_index": 8, "quality_score": 0.9, "processing_mode": "simplified_collaborative"}, {"id": "c56dbb17", "prompt": "\ndef select_words(s, n):\n    \"\"\"Given a string s and a natural number n, you have been tasked to implement \n    a function that returns a list of all words from string s that contain exactly \n    n consonants, in order these words appear in the string s.\n    If the string s is empty then the function should return an empty list.\n    Note: you may assume the input string contains only letters and spaces.\n    Examples:\n    select_words(\"<PERSON> had a little lamb\", 4) ==> [\"little\"]\n    select_words(\"<PERSON> had a little lamb\", 3) ==> [\"<PERSON>\", \"lamb\"]\n    select_words(\"simple white space\", 2) ==> []\n    select_words(\"Hello world\", 4) ==> [\"world\"]\n    select_words(\"Uncle sam\", 3) ==> [\"Uncle\"]\n    \"\"\"\n", "solution": "    def count_consonants(word: str) -> int:\n        \"\"\"Counts the number of consonants in a given word, case-insensitively.\"\"\"\n        consonants = \"bcdfghjklmnpqrstvwxyz\"\n        return sum(1 for char in word.lower() if char in consonants)\n    ", "execution_time": 73.27851104736328, "code": "def count_consonants(word: str) -> int:\n        \"\"\"Counts the number of consonants in a given word, case-insensitively.\"\"\"\n        consonants = \"bcdfghjklmnpqrstvwxyz\"\n        return sum(1 for char in word.lower() if char in consonants)", "problem_index": 9, "quality_score": 0.85, "processing_mode": "simplified_collaborative"}]}