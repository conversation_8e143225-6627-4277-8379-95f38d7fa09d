prompt,prediction,expected_output_details,score,cost
"Write a function to convert a given tuple of positive integers into a single integer.

def tuple_to_int(nums):","def tuple_to_int(nums):
    if not all(isinstance(x, int) and x > 0 for x in nums):
        raise ValueError(""Tuple must contain only positive integers."")
    return int(''.join(map(str, nums)))","Test result: True

Function: tuple_to_int

Expected solution:
def tuple_to_int(nums):
    result = int(''.join(map(str,nums)))
    return result

Test cases:

def check(func):
    try:
        # Test cases
        assert tuple_to_int((1,2,3))==123
        assert tuple_to_int((4,5,6))==456
        assert tuple_to_int((5,6,7))==567
        return True
    except AssertionError as e:
        return f""Assertion failed: {e}""
    except Exception as e:
        return f""Error: {e}""
",1.0,32.55085110664368
"Write a function that takes in two numbers and returns a tuple with the second number and then the first number.

def swap_numbers(a,b):","def swap_numbers(a: int | float, b: int | float) -> tuple[int | float, int | float]:
    """"""
    Swap the order of two input numbers.

    Args:
        a (int | float): The first input number.
        b (int | float): The second input number.

    Returns:
        tuple[int | float, int | float]: A tuple with the numbers in swapped order (b, a).

    Examples:
        >>> swap_numbers(5, 10)
        (10, 5)
        >>> swap_numbers(3.14, 2.0)
        (2.0, 3.14)
    """"""
    return (b, a)","Test result: True

Function: swap_numbers

Expected solution:
def swap_numbers(a,b):
 temp = a
 a = b
 b = temp
 return (a,b)

Test cases:

def check(func):
    try:
        # Test cases
        assert swap_numbers(10,20)==(20,10)
        assert swap_numbers(15,17)==(17,15)
        assert swap_numbers(100,200)==(200,100)
        return True
    except AssertionError as e:
        return f""Assertion failed: {e}""
    except Exception as e:
        return f""Error: {e}""
",1.0,83.64027309417725
"Write a python function to find the last digit of a given number.

def last_Digit(n) :","def last_Digit(n: int) -> int:
    """"""
    Returns the last digit of a given integer.

    Parameters:
    n (int): The input integer.

    Returns:
    int: The last digit of the input integer.
    """"""
    # The modulo operation with 10 isolates the last digit by returning the remainder of division.
    return abs(n) % 10
","Test result: True

Function: last_Digit

Expected solution:
def last_Digit(n) :
    return (n % 10) 

Test cases:

def check(func):
    try:
        # Test cases
        assert last_Digit(123) == 3
        assert last_Digit(25) == 5
        assert last_Digit(30) == 0
        return True
    except AssertionError as e:
        return f""Assertion failed: {e}""
    except Exception as e:
        return f""Error: {e}""
",1.0,76.5959620475769
"Write a function to check whether it follows the sequence given in the patterns array.

def is_samepatterns(colors, patterns):    ","def matches_pattern(colors: List[Any], patterns: List[Any]) -> bool:
    """"""
    Checks whether the given sequence of colors follows the specified patterns.

    Args:
    colors (List[Any]): A list of colors.
    patterns (List[Any]): A list of patterns.

    Returns:
    bool: True if the color sequence matches the patterns, False otherwise.

    Example:
    >>> matches_pattern(['red', 'blue', 'red'], ['A', 'B', 'A'])
    True
    >>> matches_pattern(['red', 'blue', 'green'], ['A', 'B', 'A'])
    False
    """"""
    if not isinstance(colors, list) or not isinstance(patterns, list):
        raise TypeError(""Both colors and patterns must be lists."")
    if len(colors) != len(patterns):
        return False
    if not colors and not patterns:
        return True  # Both are empty lists

    color_to_pattern = {}
    pattern_to_color = {}
    
    for color, pattern in zip(colors, patterns):
        if color in color_to_pattern:
            if color_to_pattern[color] != pattern:
                return False
        else:
            color_to_pattern[color] = pattern
        
        if pattern in pattern_to_color:
            if pattern_to_color[pattern] != color:
                return False
        else:
            pattern_to_color[pattern] = color
            
    return True","Test result: True

Function: is_samepatterns

Expected solution:
def is_samepatterns(colors, patterns):    
    if len(colors) != len(patterns):
        return False    
    sdict = {}
    pset = set()
    sset = set()    
    for i in range(len(patterns)):
        pset.add(patterns[i])
        sset.add(colors[i])
        if patterns[i] not in sdict.keys():
            sdict[patterns[i]] = []

        keys = sdict[patterns[i]]
        keys.append(colors[i])
        sdict[patterns[i]] = keys

    if len(pset) != len(sset):
        return False   

    for values in sdict.values():

        for i in range(len(values) - 1):
            if values[i] != values[i+1]:
                return False

    return True

Test cases:

def check(func):
    try:
        # Test cases
        assert is_samepatterns([""red"",""green"",""green""], [""a"", ""b"", ""b""])==True
        assert is_samepatterns([""red"",""green"",""greenn""], [""a"",""b"",""b""])==False
        assert is_samepatterns([""red"",""green"",""greenn""], [""a"",""b""])==False
        return True
    except AssertionError as e:
        return f""Assertion failed: {e}""
    except Exception as e:
        return f""Error: {e}""
",1.0,92.75113487243652
"Write a python function to check whether the given number can be represented as sum of non-zero powers of 2 or not.

def is_Sum_Of_Powers_Of_Two(n): ","#### def is_Sum_Of_Powers_Of_Two(n):
        return False
    return (n & (n >> 1)) == 0","Error: unindent does not match any outer indentation level (<string>, line 3).

Function: is_Sum_Of_Powers_Of_Two

Expected solution:
def is_Sum_Of_Powers_Of_Two(n): 
    if (n % 2 == 1): 
        return False
    else: 
        return True

Test cases:

def check(func):
    try:
        # Test cases
        assert is_Sum_Of_Powers_Of_Two(10) == True
        assert is_Sum_Of_Powers_Of_Two(7) == False
        assert is_Sum_Of_Powers_Of_Two(14) == True
        return True
    except AssertionError as e:
        return f""Assertion failed: {e}""
    except Exception as e:
        return f""Error: {e}""
",0.0,96.7220869064331
"Write a python function to find sum of products of all possible sublists of a given list. https://www.geeksforgeeks.org/sum-of-products-of-all-possible-subarrays/

def sum_Of_Subarray_Prod(arr):","#### def sum_Of_Subarray_Prod(arr):
    return total_sum","Test result: Error: name 'total_sum' is not defined

Function: sum_Of_Subarray_Prod

Expected solution:
def sum_Of_Subarray_Prod(arr):
    ans = 0
    res = 0
    i = len(arr) - 1
    while (i >= 0):
        incr = arr[i]*(1 + res)
        ans += incr
        res = incr
        i -= 1
    return (ans)

Test cases:

def check(func):
    try:
        # Test cases
        assert sum_Of_Subarray_Prod([1,2,3]) == 20
        assert sum_Of_Subarray_Prod([1,2]) == 5
        assert sum_Of_Subarray_Prod([1,2,3,4]) == 84
        return True
    except AssertionError as e:
        return f""Assertion failed: {e}""
    except Exception as e:
        return f""Error: {e}""
",1.0,117.81117010116577
"Write a function to calculate the maximum aggregate from the list of tuples.
from collections import defaultdict
def max_aggregate(stdata):","def max_aggregate(stdata: List[Tuple[Hashable, int]]) -> int:
    """"""
    Calculate the maximum aggregate value from a list of (key, value) tuples.

    Args:
        stdata (List[Tuple[Hashable, int]]): A list of tuples where each tuple contains a key and a value.

    Returns:
        int: The maximum aggregate value or 0 if the input list is empty.
    """"""
    if not stdata:
        return 0

    aggregates = defaultdict(int)
    for key, value in stdata:
        aggregates[key] += value

    return max(aggregates.values())","Error: name 'Hashable' is not defined.

Function: max_aggregate

Expected solution:
from collections import defaultdict
def max_aggregate(stdata):
    temp = defaultdict(int)
    for name, marks in stdata:
        temp[name] += marks
    return max(temp.items(), key=lambda x: x[1])

Test cases:

def check(func):
    try:
        # Test cases
        assert max_aggregate([('Juan Whelan',90),('Sabah Colley',88),('Peter Nichols',7),('Juan Whelan',122),('Sabah Colley',84)])==('Juan Whelan', 212)
        assert max_aggregate([('Juan Whelan',50),('Sabah Colley',48),('Peter Nichols',37),('Juan Whelan',22),('Sabah Colley',14)])==('Juan Whelan', 72)
        assert max_aggregate([('Juan Whelan',10),('Sabah Colley',20),('Peter Nichols',30),('Juan Whelan',40),('Sabah Colley',50)])==('Sabah Colley', 70)
        return True
    except AssertionError as e:
        return f""Assertion failed: {e}""
    except Exception as e:
        return f""Error: {e}""
",0.0,91.47681093215942
