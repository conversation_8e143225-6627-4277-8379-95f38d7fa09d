{"session_id": "problem_1", "task_type": "math", "question_id": "problem_1", "start_time": 1753219618.577408, "end_time": 1753219721.686582, "total_duration": 103.10917401313782, "total_rounds": 0, "final_quality_score": 0.9, "success": true, "total_token_usage": {"input_tokens": 822, "output_tokens": 606, "total_tokens": 1428}, "efficiency_metrics": {"tokens_per_second": 13.849398112897783, "operations_per_round": 0.0, "average_context_length": 0.0, "compression_efficiency": 0.0}, "operations": [{"operation_id": "1753219618577427", "operation_type": "collaboration_round", "agent_id": "round_1", "start_time": 1753219618.5774279, "end_time": 1753219618.578482, "duration": 0.0010540485382080078, "token_usage": {"input_tokens": 0, "output_tokens": 0, "total_tokens": 0}, "context_length_before": 0, "context_length_after": 0, "context_compression_ratio": 1.0, "quality_score": 0.0, "success": true, "error_message": "", "round_number": 1}, {"operation_id": "1753219618578485", "operation_type": "worker_draft", "agent_id": "openai", "start_time": 1753219618.578486, "end_time": 1753219618.832064, "duration": 0.25357794761657715, "token_usage": {"input_tokens": 0, "output_tokens": 0, "total_tokens": 0}, "context_length_before": 175, "context_length_after": 0, "context_compression_ratio": 1.0, "quality_score": 0.0, "success": true, "error_message": "", "round_number": 0}, {"operation_id": "1753219618832122", "operation_type": "worker_draft", "agent_id": "anthropic", "start_time": 1753219618.832129, "end_time": 1753219618.8544939, "duration": 0.02236485481262207, "token_usage": {"input_tokens": 0, "output_tokens": 0, "total_tokens": 0}, "context_length_before": 175, "context_length_after": 0, "context_compression_ratio": 1.0, "quality_score": 0.0, "success": true, "error_message": "", "round_number": 0}, {"operation_id": "1753219618854506", "operation_type": "worker_draft", "agent_id": "llama", "start_time": 1753219618.854509, "end_time": 1753219621.1088362, "duration": 2.2543270587921143, "token_usage": {"input_tokens": 224, "output_tokens": 262, "total_tokens": 486}, "context_length_before": 175, "context_length_after": 0, "context_compression_ratio": 1.0, "quality_score": 0.8, "success": true, "error_message": "", "round_number": 0}, {"operation_id": "1753219653330845", "operation_type": "collaboration_round", "agent_id": "round_2", "start_time": 1753219653.330854, "end_time": 1753219653.331264, "duration": 0.0004100799560546875, "token_usage": {"input_tokens": 0, "output_tokens": 0, "total_tokens": 0}, "context_length_before": 295, "context_length_after": 0, "context_compression_ratio": 1.0, "quality_score": 0.0, "success": true, "error_message": "", "round_number": 2}, {"operation_id": "1753219653331271", "operation_type": "worker_draft", "agent_id": "openai", "start_time": 1753219653.331274, "end_time": 1753219653.3339431, "duration": 0.002669095993041992, "token_usage": {"input_tokens": 0, "output_tokens": 0, "total_tokens": 0}, "context_length_before": 549, "context_length_after": 0, "context_compression_ratio": 1.0, "quality_score": 0.0, "success": true, "error_message": "", "round_number": 0}, {"operation_id": "1753219653333948", "operation_type": "worker_draft", "agent_id": "anthropic", "start_time": 1753219653.333951, "end_time": 1753219653.3350348, "duration": 0.0010838508605957031, "token_usage": {"input_tokens": 0, "output_tokens": 0, "total_tokens": 0}, "context_length_before": 549, "context_length_after": 0, "context_compression_ratio": 1.0, "quality_score": 0.0, "success": true, "error_message": "", "round_number": 0}, {"operation_id": "1753219653335038", "operation_type": "worker_draft", "agent_id": "llama", "start_time": 1753219653.33504, "end_time": 1753219655.3562508, "duration": 2.0212106704711914, "token_usage": {"input_tokens": 598, "output_tokens": 344, "total_tokens": 942}, "context_length_before": 549, "context_length_after": 0, "context_compression_ratio": 1.0, "quality_score": 0.8, "success": true, "error_message": "", "round_number": 0}]}