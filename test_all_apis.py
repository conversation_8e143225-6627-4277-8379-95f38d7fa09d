#!/usr/bin/env python3
"""
Quick API connectivity test for all providers
Tests basic connectivity and response for each configured API
"""

import os
import sys
import asyncio
import time
from typing import Dict, List, Tuple

# Add project path
sys.path.append('/Users/<USER>/Desktop/LLM_MAS')

from config import PROVIDER_IDS, DEFAULT_MODELS, API_KEYS
from utils.api import generate_completion, async_generate_completion

# Test prompt - simple and fast
TEST_PROMPT = "Hello, respond with just 'OK'"
SYSTEM_PROMPT = "You are a helpful assistant. Keep responses very brief."

def test_sync_api(provider_id: str) -> Tuple[bool, str, float]:
    """Test synchronous API call for a provider"""
    if not API_KEYS.get(provider_id):
        return False, f"No API key found for {provider_id}", 0.0
    
    start_time = time.time()
    try:
        response = generate_completion(
            agent_id=provider_id,
            prompt=TEST_PROMPT,
            system_prompt=SYSTEM_PROMPT,
            temperature=0.1,
            max_tokens=10,
            skip_cache=True
        )
        
        elapsed = time.time() - start_time
        
        if response and len(response.strip()) > 0:
            return True, f"✅ Response: {response.strip()[:50]}", elapsed
        else:
            return False, "❌ Empty or None response", elapsed
            
    except Exception as e:
        elapsed = time.time() - start_time
        return False, f"❌ Error: {str(e)[:100]}", elapsed

async def test_async_api(provider_id: str) -> Tuple[bool, str, float]:
    """Test asynchronous API call for a provider"""
    if not API_KEYS.get(provider_id):
        return False, f"No API key found for {provider_id}", 0.0
    
    start_time = time.time()
    try:
        response = await async_generate_completion(
            agent_id=provider_id,
            prompt=TEST_PROMPT,
            system_prompt=SYSTEM_PROMPT,
            temperature=0.1,
            max_tokens=10,
            skip_cache=True
        )
        
        elapsed = time.time() - start_time
        
        if response and len(response.strip()) > 0:
            return True, f"✅ Response: {response.strip()[:50]}", elapsed
        else:
            return False, "❌ Empty or None response", elapsed
            
    except Exception as e:
        elapsed = time.time() - start_time
        return False, f"❌ Error: {str(e)[:100]}", elapsed

def print_header():
    """Print test header"""
    print("=" * 80)
    print("🚀 API Connectivity Test")
    print("=" * 80)
    print(f"Testing {len(PROVIDER_IDS)} providers: {', '.join(PROVIDER_IDS)}")
    print("-" * 80)

def print_provider_info(provider_id: str):
    """Print provider information"""
    model = DEFAULT_MODELS.get(provider_id, "unknown")
    api_key = API_KEYS.get(provider_id, "")
    key_status = "✅ Set" if api_key else "❌ Missing"
    
    print(f"\n📡 {provider_id.upper()}")
    print(f"   Model: {model}")
    print(f"   API Key: {key_status}")
    if api_key:
        print(f"   Key Preview: {api_key[:10]}...")

def print_test_result(test_type: str, success: bool, message: str, elapsed: float):
    """Print test result"""
    status = "✅ PASS" if success else "❌ FAIL"
    print(f"   {test_type}: {status} ({elapsed:.2f}s)")
    if not success or "Error" in message:
        print(f"      {message}")
    else:
        print(f"      {message}")

async def test_all_apis():
    """Test all APIs both sync and async"""
    print_header()
    
    results = {
        'sync': {},
        'async': {},
        'summary': {'total': len(PROVIDER_IDS), 'sync_pass': 0, 'async_pass': 0}
    }
    
    for provider_id in PROVIDER_IDS:
        print_provider_info(provider_id)
        
        # Test sync API
        print("   Testing synchronous API...")
        sync_success, sync_message, sync_time = test_sync_api(provider_id)
        print_test_result("Sync", sync_success, sync_message, sync_time)
        results['sync'][provider_id] = {'success': sync_success, 'time': sync_time, 'message': sync_message}
        
        if sync_success:
            results['summary']['sync_pass'] += 1
        
        # Test async API
        print("   Testing asynchronous API...")
        async_success, async_message, async_time = await test_async_api(provider_id)
        print_test_result("Async", async_success, async_message, async_time)
        results['async'][provider_id] = {'success': async_success, 'time': async_time, 'message': async_message}
        
        if async_success:
            results['summary']['async_pass'] += 1
        
        print("-" * 40)
    
    return results

def print_summary(results: Dict):
    """Print test summary"""
    print("\n" + "=" * 80)
    print("📊 TEST SUMMARY")
    print("=" * 80)
    
    total = results['summary']['total']
    sync_pass = results['summary']['sync_pass']
    async_pass = results['summary']['async_pass']
    
    print(f"Total Providers: {total}")
    print(f"Sync API Success: {sync_pass}/{total} ({sync_pass/total*100:.1f}%)")
    print(f"Async API Success: {async_pass}/{total} ({async_pass/total*100:.1f}%)")
    
    # Show working providers
    working_sync = [p for p, r in results['sync'].items() if r['success']]
    working_async = [p for p, r in results['async'].items() if r['success']]
    
    if working_sync:
        print(f"\n✅ Working Sync APIs: {', '.join(working_sync)}")
    if working_async:
        print(f"✅ Working Async APIs: {', '.join(working_async)}")
    
    # Show failed providers
    failed_sync = [p for p, r in results['sync'].items() if not r['success']]
    failed_async = [p for p, r in results['async'].items() if not r['success']]
    
    if failed_sync:
        print(f"\n❌ Failed Sync APIs: {', '.join(failed_sync)}")
    if failed_async:
        print(f"❌ Failed Async APIs: {', '.join(failed_async)}")
    
    # Performance info
    if working_sync:
        avg_sync_time = sum(results['sync'][p]['time'] for p in working_sync) / len(working_sync)
        print(f"\n⚡ Average Sync Response Time: {avg_sync_time:.2f}s")
    
    if working_async:
        avg_async_time = sum(results['async'][p]['time'] for p in working_async) / len(working_async)
        print(f"⚡ Average Async Response Time: {avg_async_time:.2f}s")
    
    print("=" * 80)

async def main():
    """Main test function"""
    start_time = time.time()
    
    try:
        results = await test_all_apis()
        print_summary(results)
        
        total_time = time.time() - start_time
        print(f"\n🏁 Total test time: {total_time:.2f}s")
        
        # Return success if at least one API works
        success = results['summary']['sync_pass'] > 0 or results['summary']['async_pass'] > 0
        return 0 if success else 1
        
    except Exception as e:
        print(f"\n💥 Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
